import tkinter as tk
from tkinter import ttk, scrolledtext
import threading
import queue,json
import time,openpyxl
import concurrent.futures  # 新增：导入线程池模块
import os
import hashlib
import requests
import random
import re

class PacketListenerApp:
    def __init__(self, root):
        self.root = root
        root.title("网页抓包分析工具 V3.10")
        root.geometry("720x720")
        self.root.minsize(700, 700)  # 设置最小窗口大小
        
        # 创建消息队列
        self.message_queue = queue.Queue()
        self.last_progress_update_time = time.time()  # 添加进度更新时间变量
        
        # 新增：胜负彩期数变量
        self.sfb_issue_var = tk.StringVar(value="2025088")  # 默认值为2025088
        
        # 创建日志变量
        self.log_buffer = []
        self.detail_log_buffer = []
        self.detail_log_last_update = time.time()  # 添加最后更新时间
        
        # 创建进度条变量
        self.progress_var = tk.DoubleVar()
        
        # 初始化事件变量
        self.stop_event = threading.Event()
        self.detail_stop_event = threading.Event()  # 新增：明细数据抓取停止事件
        
        # URL和API相关变量
        self.api_url = "https://www.zqcf918.com/new/website/real/time/competition"  # 将URL写为常量
        self.detail_api_urls = {
            "亚盘": "https://www.zqcf918.com/new/match/v11/indexNumber/getAsianIndexNumberListByH5",
            "欧盘": "https://www.zqcf918.com/new/match/v11/indexNumber/getEuropeIndexNumberListByH5",
            "大小盘": "https://www.zqcf918.com/new/match/v11/indexNumber/getBallIndexNumberListByH5"
        }
        self.detail_interval_var = tk.StringVar(value="2")  # 修改：明细抓取间隔，默认2秒
        self.match_filter_var = tk.StringVar(value="精简")  # 新增：赛事筛选选择
        self.match_type_var = tk.StringVar(value="一级赛事")  # 新增：赛事类型选择
        self.main_match_map = {}  # 新增：主页面比赛ID映射
        
        # 初始化配置变量
        self.min_main_bet_var = tk.StringVar(value="1.70")  # 最小主赔值
        self.min_change_value_var = tk.StringVar(value="0.03")  # 最小变化值
        self.check_vars = {}  # 复选框变量，将在UI中设置
        
        # 新增：赛事筛选选择
        self.listen_duration_var = tk.StringVar(value="600")  # 默认60秒
        self.listen_interval_var = tk.StringVar(value="100")  # 默认10秒
        self.last_response_json = None  # 新增：保存最后一个响应包json

        # 新增：赛事筛选变量
        self.match_display_filter_var = tk.StringVar(value="只显示有数据赛事")  # 赛事显示筛选
        self.detail_data = []  # 修改：统一存储所有类型的盘口数据
        self.odds_type_var = tk.StringVar(value="亚盘")  # 新增：盘口类型选择
        self.company_var = tk.StringVar(value="HG")  # 新增：公司选择，默认HG
        self.company_id_map = {  # 新增：公司ID映射
            "HG": "3",
            "AC": "1",
            "WL": "10"
        }
        self.thread_pool_size_var = tk.StringVar(value="50")  # 修改：降低线程池默认大小，避免高并发导致的连接失败
        self.split_match_count_var = tk.StringVar(value="5")  # 新增：分割比赛场次，默认5
        self.all_odds_data = {  # 新增：一键抓取数据存储
            "亚盘": [],
            "欧盘": [],
            "大小盘": []
        }
        # 新增：计时器相关变量
        self.fetch_start_time = None
        self.fetch_timer_running = False
        self.fetch_timer_id = None
        
        # 新增：走地大球监测相关变量
        # self.live_over_monitoring = False
        # self.live_over_last_alerts = set()
        
        # 新增：赛前大球预测相关变量
        # self.prediction_results = []
        # self.prediction_details = {}
        
        # 新增：AI预测相关变量
        self.ai_predicting = False
        self.ai_predict_thread = None
        self.ai_match_map = {}
        
        # 新增：AI预测计时器相关变量
        self.ai_predict_start_time = None
        self.ai_predict_timer_running = False
        self.ai_predict_timer_id = None

        # 新增：指数参考相关变量
        self.index_data = []  # 存储指数数据
        self.index_fetching = False  # 指数获取状态
        self.index_stop_event = threading.Event()  # 指数获取停止事件

        # 新增：设置窗口关闭事件处理
        # self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 添加自定义事件绑定
        self.root.bind("<<ProcessMessages>>", lambda e: self.process_messages_now())

        self.setup_ui()
        self.process_messages()


    def setup_ui(self):
        # 创建notebook作为主容器
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 主监听tab
        main_frame = ttk.Frame(self.notebook)
        self.notebook.add(main_frame, text="主页面")
        
        # 明细数据tab
        self.detail_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.detail_frame, text="明细数据")
        
        # AI预测tab
        self.ai_predict_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.ai_predict_tab, text="AI预测")
        self.setup_ai_predict_ui(self.ai_predict_tab)

        # 指数参考tab
        self.index_reference_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.index_reference_tab, text="指数参考")
        self.setup_index_reference_ui(self.index_reference_tab)

        # 设置主监听tab的UI
        self.setup_main_ui(main_frame)
        # 设置明细数据tab的UI
        self.setup_detail_ui(self.detail_frame)
        
    def setup_main_ui(self, main_frame):

        # 配置区
        config_frame = ttk.LabelFrame(main_frame, text="配置区")
        config_frame.pack(fill=tk.X, padx=5, pady=5)

        # 配置行：赛事选择
        config_row = ttk.Frame(config_frame)
        config_row.pack(fill=tk.X, pady=2)
        
        # 添加赛事选择下拉框
        ttk.Label(config_row, text="赛事选择:").pack(side=tk.LEFT, padx=(15, 2))
        # 创建赛事类型变量和对应的下拉框
        match_type_combobox = ttk.Combobox(config_row, textvariable=self.match_type_var, 
                                          values=["一级赛事", "胜负彩赛事", "北单赛事", "竞彩赛事", "全部赛事"], 
                                          state="readonly", width=15)
        match_type_combobox.pack(side=tk.LEFT, padx=2)
        # 绑定变更事件
        match_type_combobox.bind("<<ComboboxSelected>>", self.on_match_type_changed)

        # 新增：胜负彩期数输入框（初始隐藏）
        self.sfb_issue_label = ttk.Label(config_row, text="胜负彩期数:")
        self.sfb_issue_entry = ttk.Entry(config_row, textvariable=self.sfb_issue_var, width=10)
        # 根据当前选择决定是否显示
        if self.match_type_var.get() == "胜负彩赛事":
            self.sfb_issue_label.pack(side=tk.LEFT, padx=(15, 2))
            self.sfb_issue_entry.pack(side=tk.LEFT, padx=2)

        # 新增：赛事筛选radio控件
        ttk.Label(config_row, text="赛事筛选:").pack(side=tk.LEFT, padx=(15, 2))

        # 创建radio按钮框架
        radio_frame = ttk.Frame(config_row)
        radio_frame.pack(side=tk.LEFT, padx=2)

        # 显示全部radio按钮
        self.show_all_radio = ttk.Radiobutton(radio_frame, text="显示全部",
                                             variable=self.match_display_filter_var,
                                             value="显示全部",
                                             command=self.on_match_filter_changed)
        self.show_all_radio.pack(side=tk.LEFT, padx=2)

        # 只显示有数据赛事radio按钮
        self.show_data_only_radio = ttk.Radiobutton(radio_frame, text="只显示有数据赛事",
                                                   variable=self.match_display_filter_var,
                                                   value="只显示有数据赛事",
                                                   command=self.on_match_filter_changed)
        self.show_data_only_radio.pack(side=tk.LEFT, padx=2)

        # 比赛选择区
        match_frame = ttk.LabelFrame(main_frame, text="比赛列表")
        match_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 比赛列表 - 使用Treeview
        match_list_frame = ttk.Frame(match_frame)
        match_list_frame.pack(fill=tk.X, expand=True, pady=2)
        
        # 创建Treeview组件
        columns = ("序号", "联赛", "对阵", "开赛时间", "比赛状态", "比分", "让球盘口", "大小盘口")
        self.main_match_tree = ttk.Treeview(match_list_frame, columns=columns, show='headings', height=8)
        
        # 设置列宽和表头
        self.main_match_tree.column("序号", width=40, anchor="center")
        self.main_match_tree.column("联赛", width=60, anchor="w")
        self.main_match_tree.column("对阵", width=138, anchor="w")
        self.main_match_tree.column("开赛时间", width=128, anchor="center")
        self.main_match_tree.column("比赛状态", width=60, anchor="center")
        self.main_match_tree.column("比分", width=60, anchor="center")
        self.main_match_tree.column("让球盘口", width=68, anchor="center")
        self.main_match_tree.column("大小盘口", width=68, anchor="center")
        
        self.main_match_tree.heading("序号", text="序号")
        self.main_match_tree.heading("联赛", text="联赛")
        self.main_match_tree.heading("对阵", text="对阵")
        self.main_match_tree.heading("开赛时间", text="开赛时间")
        self.main_match_tree.heading("比赛状态", text="比赛状态") 
        self.main_match_tree.heading("比分", text="比分")
        self.main_match_tree.heading("让球盘口", text="让球盘口")
        self.main_match_tree.heading("大小盘口", text="大小盘口")
        
        # 添加垂直滚动条
        tree_scrollbar = ttk.Scrollbar(match_list_frame, orient="vertical", command=self.main_match_tree.yview)
        self.main_match_tree.configure(yscrollcommand=tree_scrollbar.set)
        
        # 放置Treeview和滚动条
        self.main_match_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 操作区
        operation_frame = ttk.LabelFrame(main_frame, text="操作区")
        operation_frame.pack(fill=tk.X, padx=5, pady=5)

        self.start_listen_btn = ttk.Button(operation_frame, text="获取比赛数据", command=self.start_listen)
        self.start_listen_btn.pack(side=tk.LEFT, padx=5, pady=5)

        self.stop_btn = ttk.Button(operation_frame, text="终止获取", command=self.stop_listen, state=tk.DISABLED)
        self.stop_btn.pack(side=tk.LEFT, padx=5, pady=5)

        self.export_btn = ttk.Button(operation_frame, text="导出数据", command=self.export_to_excel)
        self.export_btn.pack(side=tk.RIGHT, padx=5, pady=5)

        # 显示区/日志输出
        display_frame = ttk.LabelFrame(main_frame, text="显示区/日志输出")
        display_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.log_text = scrolledtext.ScrolledText(display_frame, wrap=tk.WORD, height=25)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

        # 日志颜色配置
        self.log_text.tag_config('info', foreground='black')
        self.log_text.tag_config('success', foreground='blue')
        self.log_text.tag_config('warning', foreground='orange')
        self.log_text.tag_config('error', foreground='red')

    def log_message(self, message, msg_type='info', is_detail=False):
        # 日志内容截断，避免界面卡顿
        max_length = 1000  # 最多显示1000字符
        if isinstance(message, str) and len(message) > max_length:
            message = message[:max_length] + "... [内容过长已截断]"
        
        # 将日志消息添加到队列
        self.message_queue.put((msg_type, message, is_detail))
        
        # 对于明细数据的抓取进度日志，不要每次都立即处理，让它们累积起来批量处理
        is_progress_log = is_detail and "正在抓取" in message or "总进度" in message

        # 如果不是明细数据的进度日志，或者队列中积累了较多消息，则触发立即处理
        if not is_progress_log and self.message_queue.qsize() > 50:
            # 通知主线程立即处理消息队列
            self.root.event_generate("<<ProcessMessages>>", when="tail")

    def process_messages(self):
        try:
            # 记录批处理开始时间
            start_time = time.time()
            # 限制每次处理的消息数量
            max_messages_per_batch = 50
            messages_processed = 0
            
            # 检查是否需要更新明细日志缓冲区
            current_time = time.time()
            if current_time - self.detail_log_last_update > 0.5 and self.detail_log_buffer:  # 每0.5秒最多更新一次
                self.update_detail_log_buffer()
            
            # 处理队列中的消息，但限制处理时间不超过50ms以避免UI冻结
            while not self.message_queue.empty() and messages_processed < max_messages_per_batch and (time.time() - start_time) < 0.05:
                msg_type, message, is_detail = self.message_queue.get_nowait()
                
                if is_detail:
                    # 明细数据日志使用缓冲区
                    timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
                    self.detail_log_buffer.append((msg_type, timestamp, message))
                    
                    # 如果是错误或成功消息，或者缓冲区过大，立即更新
                    if msg_type in ('error', 'success') or len(self.detail_log_buffer) > 20:
                        self.update_detail_log_buffer()
                else:
                    # 主日志直接更新
                    log_widget = self.log_text
                    log_widget.insert(tk.END, f"{time.strftime('%Y-%m-%d %H:%M:%S')} - {message}\n", msg_type)
                    log_widget.see(tk.END)
                
                self.message_queue.task_done()
                messages_processed += 1
        except queue.Empty:
            pass
        finally:
            # 继续定时处理消息队列
            self.root.after(100, self.process_messages)

    def update_status(self, status):
        """更新状态栏文本"""
        self.status_var.set(status)
        
    def start_listen(self):
        self.log_message("准备获取比赛数据...", 'info')
        self.update_status("正在获取比赛数据...")
        self.start_listen_btn.config(state=tk.DISABLED)
        self.stop_btn.config(state=tk.NORMAL)
        threading.Thread(target=self.fetch_matches_api, daemon=True).start()

    def fetch_matches_api(self):
        """使用API请求获取比赛数据"""
        try:
            import json, hashlib, time, requests
            from datetime import datetime
            
            # 获取当前选择的赛事类型
            match_type = self.match_type_var.get()
            
            # 胜负彩赛事特殊处理
            if match_type == "胜负彩赛事":
                self.fetch_sfb_matches_api()
                return
            
            # 以下是原有的普通赛事请求逻辑
            # 使用常量API URL
            api_url = self.api_url
            if not api_url:
                self.log_message("请求URL不能为空", 'error')
                self.update_status("请求失败")
                self.root.after(0, lambda: self.start_listen_btn.config(state=tk.NORMAL))
                self.root.after(0, lambda: self.stop_btn.config(state=tk.DISABLED))
                return
            
            # 根据赛事选择确定type参数
            type_map = {
                "全部赛事": 0,
                "一级赛事": 1,
                "竞彩赛事": 2,
                "北单赛事": 3,
                "胜负彩赛事": 4
            }
            type_value = type_map.get(match_type, 1)  # 默认为一级赛事
            
            # 准备请求参数
            current_time_millis = int(round(time.time() * 1000))
            params = {
                "type": type_value,
                "leagues": [],
                "yppk": [],
                "dxpk": [],
                "param": "",
                "timeInMillis": current_time_millis,
                "timeZone": "GMT+0800",
                "platform": "web"
            }
            
            # 生成sign参数
            def generate_sign(params):
                # 删除已有的sign参数
                if 'sign' in params:
                    del params['sign']
                
                # 复制参数字典
                params_copy = params.copy()
                
                # 删除空值、"{}"或其他空对象
                for key in list(params_copy.keys()):
                    value = params_copy[key]
                    # 检查值是否为空 (None, "", [], {})
                    if not value and value != 0:
                        del params_copy[key]
                    # 检查值是否为"{}"
                    elif json.dumps(value) == "{}":
                        del params_copy[key]
                
                # 按键名排序
                sorted_keys = sorted(params_copy.keys())
                
                # 构建签名字符串
                sign_str = ""
                for key in sorted_keys:
                    sign_str += str(params_copy[key]) + "&"
                
                # 添加密钥并计算MD5
                sign_str += "FE1C636C5A855EA4"
                
                # 计算MD5并转为大写
                sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
                
                return sign
            
            # 添加sign参数
            params['sign'] = generate_sign(params)
            
            # 构建完整的请求体
            request_body = {"params": params}
            
            # 设置请求头
            headers = {
                'Host': 'www.zqcf918.com',
                'Cookie': 'aliyungf_tc=6d3f706181aefebab22188e690a5e8af00edd503429d1c86a9a8e3d72c4fec8e; Secure=1',
                'Accept': 'application/json',
                'Content-Type': 'application/json;charset=UTF-8',
                'Sec-Ch-Ua-Mobile': '?0',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.6167.85 Safari/537.36',
                'Sec-Ch-Ua-Platform': 'Windows',
                'Origin': 'https://www.zqcf918.com',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Referer': 'https://www.zqcf918.com/',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Priority': 'u=1, i',
            }
            
            # 发送请求
            self.log_message("正在发送请求...", 'info')
            response = requests.post(api_url, json=request_body, headers=headers)
            
            # 处理响应
            if response.status_code == 200:
                self.log_message("请求成功", 'success')
                response_json = response.json()
                self.last_response_json = response_json
                
                # 打印部分响应内容
                if 'data' in response_json and 'list' in response_json['data']:
                    match_list = response_json['data']['list']
                    self.log_message(f"获取到 {len(match_list)} 场比赛", 'success')
                    
                    # 更新比赛列表
                    self.update_main_match_tree(match_list)
                else:
                    self.log_message("响应格式不符合预期", 'warning')
            else:
                self.log_message(f"请求失败，状态码: {response.status_code}", 'error')
                self.log_message(f"响应内容: {response.text}", 'error')
            
            self.update_status("数据获取完成")
        except Exception as e:
            self.log_message(f"获取比赛数据时发生异常: {str(e)}", 'error')
            self.update_status("数据获取异常")
        finally:
            self.root.after(0, lambda: self.start_listen_btn.config(state=tk.NORMAL))
            self.root.after(0, lambda: self.stop_btn.config(state=tk.DISABLED))
    
    def update_main_match_tree(self, match_list):
        """更新主页面比赛列表"""
        # 清空当前列表
        self.main_match_tree.delete(*self.main_match_tree.get_children())

        # 清空比赛ID映射
        self.main_match_map = {}

        # 获取当前筛选设置
        filter_mode = self.match_display_filter_var.get()

        # 添加比赛到列表
        display_idx = 1  # 用于显示的序号
        for idx, match in enumerate(match_list, 1):
            match_id = match.get('ID', '')
            home_team = match.get('home', '')
            away_team = match.get('away', '')
            league = match.get('league', '')
            time_str = match.get('time', '')
            state = match.get('stateDesc', '')
            home_score = match.get('homeScore', '0')
            away_score = match.get('awayScore', '0')
            score = f"{home_score}-{away_score}"

            if match_id and home_team and away_team:
                display_text = f"{home_team} VS {away_team}"
                # 获取让球盘口信息
                handicap = match.get("JSPKDesc", "")
                # 获取大小球盘口信息
                ou_handicap = match.get("DXQDesc", "")

                # 应用筛选逻辑
                should_display = True
                if filter_mode == "只显示有数据赛事":
                    # 检查让球盘口和大小球盘口是否都为空或"-"
                    if (not handicap or handicap == "-") and (not ou_handicap or ou_handicap == "-"):
                        should_display = False

                # 只有通过筛选的比赛才显示
                if should_display:
                    item_id = self.main_match_tree.insert('', 'end', values=(display_idx, league, display_text, time_str, state, score, handicap, ou_handicap))
                    # 将match_id与item_id建立映射关系
                    self.main_match_map[item_id] = match_id
                    display_idx += 1

        # 新增：主页面更新后自动刷新明细tab比赛选择框
        if hasattr(self, 'refresh_detail_matches'):
            self.refresh_detail_matches()

        # 新增：主页面更新后自动刷新AI预测tab比赛选择框
        if hasattr(self, 'refresh_ai_matches'):
            self.refresh_ai_matches()

    def stop_listen(self):
        """停止获取数据"""
        self.log_message("终止获取数据请求...", 'warning')
        self.stop_event.set()
        self.update_status("终止获取中...")
        self.stop_btn.config(state=tk.DISABLED)
        
    def setup_detail_ui(self, detail_frame):
        # 配置区
        config_frame = ttk.LabelFrame(detail_frame, text="配置区")
        config_frame.pack(fill=tk.X, padx=5, pady=5)

        # 第一行：盘口类型选择和线程池大小
        config_row0 = ttk.Frame(config_frame)
        config_row0.pack(fill=tk.X, pady=2)
        ttk.Label(config_row0, text="盘口类型:").pack(side=tk.LEFT, padx=2)
        odds_type_combobox = ttk.Combobox(config_row0, textvariable=self.odds_type_var, values=["亚盘", "欧盘", "大小盘"], state="readonly", width=10)
        odds_type_combobox.pack(side=tk.LEFT, padx=2)
        odds_type_combobox.bind("<<ComboboxSelected>>", self.on_odds_type_changed)
        
        # 新增：公司选择下拉框
        ttk.Label(config_row0, text="公司选择:").pack(side=tk.LEFT, padx=(15, 2))
        company_combobox = ttk.Combobox(config_row0, textvariable=self.company_var, values=["HG", "AC", "WL"], state="readonly", width=10)
        company_combobox.pack(side=tk.LEFT, padx=2)
        company_combobox.bind("<<ComboboxSelected>>", self.on_company_changed)
        
        # 新增：线程池大小设置
        ttk.Label(config_row0, text="线程池大小:").pack(side=tk.LEFT, padx=(15, 2))
        ttk.Entry(config_row0, textvariable=self.thread_pool_size_var, width=5).pack(side=tk.LEFT, padx=2)

        # 将"数据抓取已用时"控件移到线程池大小控件右侧
        self.fetch_duration_var = tk.StringVar(value="数据抓取已用时: 00:00:00")
        self.fetch_duration_label = ttk.Label(config_row0, textvariable=self.fetch_duration_var)
        self.fetch_duration_label.pack(side=tk.LEFT, padx=15)
        
        # 第二行：抓取间隔
        config_row1 = ttk.Frame(config_frame)
        config_row1.pack(fill=tk.X, pady=2)
        ttk.Label(config_row1, text="抓取间隔(秒):").pack(side=tk.LEFT, padx=2)
        ttk.Entry(config_row1, textvariable=self.detail_interval_var, width=8).pack(side=tk.LEFT, padx=2)

        # 新增：比赛选择区
        match_frame = ttk.LabelFrame(detail_frame, text="比赛选择")
        match_frame.pack(fill=tk.X, padx=5, pady=5)
        match_list_frame = ttk.Frame(match_frame)
        match_list_frame.pack(fill=tk.X, expand=True, pady=2)
        columns = ("序号", "联赛", "对阵", "开赛时间", "比赛状态", "比分", "让球盘口", "大小盘口")
        self.detail_match_tree = ttk.Treeview(match_list_frame, columns=columns, show='headings', height=8)
        self.detail_match_tree.column("序号", width=40, anchor="center")
        self.detail_match_tree.column("联赛", width=60, anchor="w")
        self.detail_match_tree.column("对阵", width=138, anchor="w")
        self.detail_match_tree.column("开赛时间", width=128, anchor="center")
        self.detail_match_tree.column("比赛状态", width=60, anchor="center")
        self.detail_match_tree.column("比分", width=60, anchor="center")
        self.detail_match_tree.column("让球盘口", width=68, anchor="center")
        self.detail_match_tree.column("大小盘口", width=68, anchor="center")
        self.detail_match_tree.heading("序号", text="序号")
        self.detail_match_tree.heading("联赛", text="联赛")
        self.detail_match_tree.heading("对阵", text="对阵")
        self.detail_match_tree.heading("开赛时间", text="开赛时间")
        self.detail_match_tree.heading("比赛状态", text="比赛状态")
        self.detail_match_tree.heading("比分", text="比分")
        self.detail_match_tree.heading("让球盘口", text="让球盘口")
        self.detail_match_tree.heading("大小盘口", text="大小盘口")
        tree_scrollbar = ttk.Scrollbar(match_list_frame, orient="vertical", command=self.detail_match_tree.yview)
        self.detail_match_tree.configure(yscrollcommand=tree_scrollbar.set)
        self.detail_match_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        ttk.Button(match_frame, text="刷新比赛", command=self.refresh_detail_matches).pack(side=tk.LEFT, padx=5)
        ttk.Button(match_frame, text="全选", command=self.select_all_detail_matches).pack(side=tk.LEFT, padx=5)
        ttk.Button(match_frame, text="取消选择", command=self.deselect_all_detail_matches).pack(side=tk.LEFT, padx=5)
        self.detail_match_map = {}  # item_id -> match_id

        # 操作区
        operation_frame = ttk.LabelFrame(detail_frame, text="操作区")
        operation_frame.pack(fill=tk.X, padx=5, pady=5)

        # 保留一键抓取按钮
        self.one_click_fetch_btn = ttk.Button(operation_frame, text="一键抓取", command=self.one_click_fetch)
        self.one_click_fetch_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 添加停止抓取按钮
        self.stop_detail_btn = ttk.Button(operation_frame, text="停止抓取", command=self.stop_detail_fetch, state=tk.DISABLED)
        self.stop_detail_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        # 保留清空日志按钮
        self.clear_log_btn = ttk.Button(operation_frame, text="清空日志", command=self.clear_detail_log)
        self.clear_log_btn.pack(side=tk.LEFT, padx=5, pady=5)

        # 保留导出数据按钮
        self.export_detail_btn = ttk.Button(operation_frame, text="导出数据", command=self.export_detail_data)
        self.export_detail_btn.pack(side=tk.RIGHT, padx=5, pady=5)

        # 显示区
        display_frame = ttk.LabelFrame(detail_frame, text="显示区/日志输出")
        display_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建明细日志文本框，使用自定义的更高效的显示方式
        self.detail_log_text = scrolledtext.ScrolledText(display_frame, wrap=tk.WORD, height=25)
        self.detail_log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 添加自动更新防抖动
        self.detail_log_text.bind("<KeyRelease>", lambda e: self.root.after(100, self.detail_log_text.see, tk.END))

        # 日志颜色配置
        self.detail_log_text.tag_config('info', foreground='black')
        self.detail_log_text.tag_config('success', foreground='blue')
        self.detail_log_text.tag_config('warning', foreground='orange')
        self.detail_log_text.tag_config('error', foreground='red')
        
        # 添加一个更新缓冲区，用于批量更新
        self.detail_log_buffer = []
        self.detail_log_last_update = time.time()
    
    def clear_detail_log(self):
        """清空明细数据tab的日志显示区"""
        self.detail_log_text.delete(1.0, tk.END)
        self.log_message("日志已清空", 'info', True)

    def start_detail_fetch(self):
        """保留接口但无实际功能（已废弃）"""
        self.log_message("单个盘口数据抓取功能已移除，请使用'一键抓取'功能", 'warning', True)
        
    def _detail_fetch_thread_pool(self):
        """保留接口但无实际功能（已废弃）"""
        pass
    
    def export_detail_data(self):
        # 检查是否有数据
        if not self.detail_data:
            self.log_message("暂无可导出的明细数据", 'warning')
            return
            
        import tkinter.filedialog as fd
        import json
        try:
            from openpyxl import Workbook
        except ImportError:
            self.log_message("未安装openpyxl库，请先安装：pip install openpyxl", 'error')
            return

        # 获取当前公司信息
        company = self.company_var.get()
        
        # 选择保存路径
        file_path = fd.asksaveasfilename(defaultextension=".xlsx", filetypes=[("Excel文件", "*.xlsx")], title="保存明细数据Excel文件", initialfile=f"detail_data_{company}.xlsx")
        if not file_path:
            return
            
        try:
            wb = Workbook()
            
            # 首先创建一个包含主页面"导出数据"按钮数据的sheet
            first_sheet = wb.active
            first_sheet.title = "比赛数据"
            
            # 设定需要导出的字段（与export_to_excel方法保持一致）
            headers = [
                "序号","比赛ID","联赛", "主队", "客队", "主队得分", "客队得分", "开赛时间", "比赛状态","主队赔率","让球盘口", "客队赔率","大球赔率","大小球盘口","小球赔率"
            ]
            first_sheet.append(headers)
            
            # 获取主页面数据
            match_list = None
            if isinstance(self.last_response_json, dict) and 'data' in self.last_response_json and 'list' in self.last_response_json['data']:
                match_list = self.last_response_json['data']['list']
            elif isinstance(self.last_response_json, dict) and 'list' in self.last_response_json:
                match_list = self.last_response_json['list']
            elif isinstance(self.last_response_json, list):
                match_list = self.last_response_json
            
            # 填充主页面数据
            if match_list:
                for idx, match in enumerate(match_list, 1):
                    row = [
                        idx,
                        match.get("ID", ""),
                        match.get("league", ""),
                        match.get("home", ""),
                        match.get("away", ""),
                        match.get("homeScore", ""),
                        match.get("awayScore", ""),
                        match.get("time", ""),
                        match.get("stateDesc", ""),
                        match.get("HJSPL", ""), # 主队赔率
                        match.get("JSPKDesc", ""),
                        match.get("WJSPL", ""), # 客队赔率
                        match.get("DXQ_HJSPL", ""), # 大球赔率
                        match.get("DXQDesc", ""),
                        match.get("DXQ_WJSPL", ""), # 小球赔率         
                    ]
                    first_sheet.append(row)
            
            # 调整列宽
            for column in first_sheet.columns:
                max_length = 0
                column = list(column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2)
                first_sheet.column_dimensions[column[0].column_letter].width = adjusted_width
            
            # 创建第二个sheet用于明细数据
            odds_type = self.odds_type_var.get()
            ws = wb.create_sheet(title=f"{odds_type}明细数据")
            
            # 设置表头
            headers_map = {
                "亚盘": ["比赛ID", "数据类型", "比赛分钟", "比分", "主队赔率", "盘口", "客队赔率",
                        "状态", "是否封盘", "变化时间"],
                "欧盘": ["比赛ID", "数据类型", "比赛分钟", "比分", "主胜赔率", "平局", "主负赔率",
                        "状态", "是否封盘", "变化时间"],
                "大小盘": ["比赛ID", "数据类型", "比赛分钟", "比分", "大球赔率", "盘口", "小球赔率",
                         "状态", "是否封盘", "变化时间"]
            }
            headers = headers_map.get(odds_type, headers_map["亚盘"])
            ws.append(headers)
            
            # 数据类型中文名称映射
            type_names = {
                'roll': '走地盘',
                'index': '即时盘',
                'breakfast': '早盘'
            }
            
            # 按时间排序数据
            sorted_data = sorted(self.detail_data, key=lambda x: x.get('createTime', 0))
            
            # 写入明细数据
            for data in sorted_data:
                # 根据盘口类型处理数据
                if odds_type == "亚盘":
                    row = [
                        data.get("matchId", ""),
                        type_names.get(data.get("type", ""), ""),  # 数据类型
                        data.get("a", ""),  # 比赛分钟
                        data.get("b", ""),  # 比分
                        data.get("c", ""),  # 主队赔率
                        data.get("d", ""),  # 盘口
                        data.get("e", ""),  # 客队赔率
                        data.get("matchState", ""),  # 状态
                        data.get("isFeng2", ""),  # 是否封盘
                        data.get("changeTimeStr", "")  # 变化时间
                    ]
                elif odds_type == "欧盘":
                    row = [
                        data.get("matchId", ""),
                        type_names.get(data.get("type", ""), ""),  # 数据类型
                        data.get("a", ""),  # 比赛分钟
                        data.get("b", ""),  # 比分
                        data.get("c1", ""),  # 主胜赔率
                        data.get("c2", ""),  # 平局
                        data.get("c3", ""),  # 主负赔率
                        data.get("matchState", ""),  # 状态
                        data.get("isFeng2", ""),  # 是否封盘
                        data.get("changeTimeStr", "")  # 变化时间
                    ]
                else:  # 大小盘
                    row = [
                        data.get("matchId", ""),
                        type_names.get(data.get("type", ""), ""),  # 数据类型
                        data.get("a", ""),  # 比赛分钟
                        data.get("b", ""),  # 比分
                        data.get("c", ""),  # 大球赔率
                        data.get("d", ""),  # 盘口
                        data.get("e", ""),  # 小球赔率
                        data.get("matchState", ""),  # 状态
                        data.get("isFeng2", ""),  # 是否封盘
                        data.get("changeTimeStr", "")  # 变化时间
                    ]
                ws.append(row)
                
            # 调整列宽
            for column in ws.columns:
                max_length = 0
                column = list(column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2)
                ws.column_dimensions[column[0].column_letter].width = adjusted_width
            
            wb.save(file_path)
            self.log_message(f"明细数据已成功导出到: {file_path}", 'success')
        except Exception as e:
            self.log_message(f"导出明细数据Excel失败: {str(e)}", 'error')
    
    def _split_export_all_odds_data(self, export_dir, match_info_dict, matches_per_file):
        """分割导出一键抓取的所有盘口数据"""
        import os
        from datetime import datetime
        from openpyxl import Workbook
        
        odds_types_order = ["亚盘", "欧盘", "大小盘"]
        
        # 获取当前公司信息
        company = self.company_var.get()
        
        # 收集所有比赛ID
        all_match_ids = set()
        for odds_type in odds_types_order:
            for item in self.all_odds_data[odds_type]:
                match_id = str(item.get('matchId', ''))
                if match_id:
                    all_match_ids.add(match_id)
        
        # 将比赛ID列表转换为有序列表
        match_ids = sorted(list(all_match_ids))
        total_matches = len(match_ids)
        
        if not total_matches:
            self.log_message("未找到有效的比赛数据", 'error', True)
            return
            
        self.log_message(f"共有{total_matches}场比赛数据，按每{matches_per_file}场分割", 'info', True)
        
        # 计算需要创建的文件数
        file_count = (total_matches + matches_per_file - 1) // matches_per_file
        
        # 获取主页面数据列表（用于每个分割文件的第一个sheet）
        match_list = None
        if isinstance(self.last_response_json, dict) and 'data' in self.last_response_json and 'list' in self.last_response_json['data']:
            match_list = self.last_response_json['data']['list']
        elif isinstance(self.last_response_json, dict) and 'list' in self.last_response_json:
            match_list = self.last_response_json['list']
        elif isinstance(self.last_response_json, list):
            match_list = self.last_response_json
        
        # 分组处理
        for file_index in range(file_count):
            start_idx = file_index * matches_per_file
            end_idx = min(start_idx + matches_per_file, total_matches)
            current_batch = match_ids[start_idx:end_idx]
            
            # 创建工作簿
            wb = Workbook()
            
            # 首先创建一个包含主页面"导出数据"按钮数据的sheet
            first_sheet = wb.active
            first_sheet.title = "比赛数据"
            
            # 设定需要导出的字段（与export_to_excel方法保持一致）
            headers = [
                "序号","比赛ID","联赛", "主队", "客队", "主队得分", "客队得分", "开赛时间", "比赛状态","主队赔率","让球盘口", "客队赔率","大球赔率","大小球盘口","小球赔率"
            ]
            first_sheet.append(headers)
            
            # 填充主页面数据
            if match_list:
                for idx, match in enumerate(match_list, 1):
                    row = [
                        idx,
                        match.get("ID", ""),
                        match.get("league", ""),
                        match.get("home", ""),
                        match.get("away", ""),
                        match.get("homeScore", ""),
                        match.get("awayScore", ""),
                        match.get("time", ""),
                        match.get("stateDesc", ""),
                        match.get("HJSPL", ""), # 主队赔率
                        match.get("JSPKDesc", ""),
                        match.get("WJSPL", ""), # 客队赔率
                        match.get("DXQ_HJSPL", ""), # 大球赔率
                        match.get("DXQDesc", ""),
                        match.get("DXQ_WJSPL", ""), # 小球赔率         
                    ]
                    first_sheet.append(row)
            
            # 调整列宽
            for column in first_sheet.columns:
                max_length = 0
                column = list(column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2)
                first_sheet.column_dimensions[column[0].column_letter].width = adjusted_width
            
            # 创建sheet顺序表，格式为: [比赛ID, 主队, 客队, 盘口类型]
            sheet_order = []
            
            for match_id in current_batch:
                for odds_type in odds_types_order:
                    # 检查该比赛该盘口类型是否有数据
                    has_data = False
                    for item in self.all_odds_data[odds_type]:
                        if str(item.get('matchId')) == match_id:
                            has_data = True
                            break
                    
                    if has_data:
                        home_team = ''
                        away_team = ''
                        if match_id in match_info_dict:
                            home_team = match_info_dict[match_id].get('home_team', '')
                            away_team = match_info_dict[match_id].get('away_team', '')
                        
                        sheet_order.append({
                            'match_id': match_id,
                            'home_team': home_team,
                            'away_team': away_team,
                            'odds_type': odds_type
                        })
            
            # 创建工作表
            for sheet_info in sheet_order:
                # 修改sheet_info中的标题格式，添加公司信息
                sheet_info_with_company = sheet_info.copy()
                sheet_info_with_company['company'] = company
                self._create_odds_sheet_with_company(wb, sheet_info_with_company, self.all_odds_data)
            
            # 保存工作簿
            file_name = f"分割导出_{company}_{file_index+1}_{start_idx+1}_到_{end_idx}.xlsx"
            file_path = os.path.join(export_dir, file_name)
            wb.save(file_path)
            self.log_message(f"成功导出第{file_index+1}个文件: {file_name}", 'success', True)
            
        self.log_message(f"分割导出完成，共生成{file_count}个文件", 'success', True)
    
    def _split_export_single_odds_data(self, export_dir, match_info_dict, matches_per_file):
        """分割导出单一盘口数据"""
        import os
        from datetime import datetime
        from openpyxl import Workbook
        
        # 获取当前公司信息
        company = self.company_var.get()
        
        # 收集所有比赛ID
        all_match_ids = set()
        for item in self.detail_data:
            match_id = str(item.get('matchId', ''))
            if match_id:
                all_match_ids.add(match_id)
        
        # 将比赛ID列表转换为有序列表
        match_ids = sorted(list(all_match_ids))
        total_matches = len(match_ids)
        
        if not total_matches:
            self.log_message("未找到有效的比赛数据", 'error', True)
            return
            
        odds_type = self.odds_type_var.get()
        self.log_message(f"共有{total_matches}场比赛{odds_type}数据，按每{matches_per_file}场分割", 'info', True)
        
        # 计算需要创建的文件数
        file_count = (total_matches + matches_per_file - 1) // matches_per_file
        
        # 获取主页面数据列表（用于每个分割文件的第一个sheet）
        match_list = None
        if isinstance(self.last_response_json, dict) and 'data' in self.last_response_json and 'list' in self.last_response_json['data']:
            match_list = self.last_response_json['data']['list']
        elif isinstance(self.last_response_json, dict) and 'list' in self.last_response_json:
            match_list = self.last_response_json['list']
        elif isinstance(self.last_response_json, list):
            match_list = self.last_response_json
        
        # 分组处理
        for file_index in range(file_count):
            start_idx = file_index * matches_per_file
            end_idx = min(start_idx + matches_per_file, total_matches)
            current_batch = match_ids[start_idx:end_idx]
            
            # 创建工作簿
            wb = Workbook()
            
            # 首先创建一个包含主页面"导出数据"按钮数据的sheet
            first_sheet = wb.active
            first_sheet.title = "比赛数据"
            
            # 设定需要导出的字段（与export_to_excel方法保持一致）
            headers = [
                "序号","比赛ID","联赛", "主队", "客队", "主队得分", "客队得分", "开赛时间", "比赛状态","主队赔率","让球盘口", "客队赔率","大球赔率","大小球盘口","小球赔率"
            ]
            first_sheet.append(headers)
            
            # 填充主页面数据
            if match_list:
                for idx, match in enumerate(match_list, 1):
                    row = [
                        idx,
                        match.get("ID", ""),
                        match.get("league", ""),
                        match.get("home", ""),
                        match.get("away", ""),
                        match.get("homeScore", ""),
                        match.get("awayScore", ""),
                        match.get("time", ""),
                        match.get("stateDesc", ""),
                        match.get("HJSPL", ""), # 主队赔率
                        match.get("JSPKDesc", ""),
                        match.get("WJSPL", ""), # 客队赔率
                        match.get("DXQ_HJSPL", ""), # 大球赔率
                        match.get("DXQDesc", ""),
                        match.get("DXQ_WJSPL", ""), # 小球赔率         
                    ]
                    first_sheet.append(row)
            
            # 调整列宽
            for column in first_sheet.columns:
                max_length = 0
                column = list(column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2)
                first_sheet.column_dimensions[column[0].column_letter].width = adjusted_width
            
            # 对每个比赛ID创建工作表
            for match_id in current_batch:
                # 获取该比赛的所有数据
                match_data = [item for item in self.detail_data if str(item.get('matchId')) == match_id]
                if not match_data:
                    continue
                
                # 设置工作表标题
                home_team = ''
                away_team = ''
                if match_id in match_info_dict:
                    home_team = match_info_dict[match_id].get('home_team', '')
                    away_team = match_info_dict[match_id].get('away_team', '')
                
                sheet_title = f"（{odds_type}-{company}）{home_team}-{away_team}"
                # 确保sheet名称不超过31个字符（Excel限制）
                if len(sheet_title) > 31:
                    sheet_title = sheet_title[:28] + "..."
                
                # 避免重复名称
                base_title = sheet_title
                suffix = 1
                while sheet_title in wb.sheetnames:
                    sheet_title = f"{base_title}_{suffix}"
                    suffix += 1
                
                # 创建工作表
                ws = wb.create_sheet(title=sheet_title)
                
                # 根据盘口类型设置表头
                headers_map = {
                    "亚盘": ["比赛ID", "数据类型", "比赛分钟", "比分", "主队赔率", "盘口", "客队赔率",
                            "状态", "是否封盘", "变化时间"],
                    "欧盘": ["比赛ID", "数据类型", "比赛分钟", "比分", "主胜赔率", "平局", "主负赔率",
                            "状态", "是否封盘", "变化时间"],
                    "大小盘": ["比赛ID", "数据类型", "比赛分钟", "比分", "大球赔率", "盘口", "小球赔率",
                             "状态", "是否封盘", "变化时间"]
                }
                headers = headers_map.get(odds_type, headers_map["亚盘"])
                ws.append(headers)
                
                # 按时间排序数据
                sorted_data = sorted(match_data, key=lambda x: x.get('createTime', 0))
                
                # 数据类型中文名称映射
                type_names = {
                    'roll': '走地盘',
                    'index': '即时盘',
                    'breakfast': '早盘'
                }
                
                # 写入数据
                for data in sorted_data:
                    # 根据盘口类型处理数据
                    if odds_type == "亚盘":
                        row = [
                            data.get("matchId", ""),
                            type_names.get(data.get("type", ""), ""),  # 数据类型
                            data.get("a", ""),  # 比赛分钟
                            data.get("b", ""),  # 比分
                            data.get("c", ""),  # 主队赔率
                            data.get("d", ""),  # 盘口
                            data.get("e", ""),  # 客队赔率
                            data.get("matchState", ""),  # 状态
                            data.get("isFeng2", ""),  # 是否封盘
                            data.get("changeTimeStr", "")  # 变化时间
                        ]
                    elif odds_type == "欧盘":
                        row = [
                            data.get("matchId", ""),
                            type_names.get(data.get("type", ""), ""),  # 数据类型
                            data.get("a", ""),  # 比赛分钟
                            data.get("b", ""),  # 比分
                            data.get("c1", ""),  # 主胜赔率
                            data.get("c2", ""),  # 平局
                            data.get("c3", ""),  # 主负赔率
                            data.get("matchState", ""),  # 状态
                            data.get("isFeng2", ""),  # 是否封盘
                            data.get("changeTimeStr", "")  # 变化时间
                        ]
                    else:  # 大小盘
                        row = [
                            data.get("matchId", ""),
                            type_names.get(data.get("type", ""), ""),  # 数据类型
                            data.get("a", ""),  # 比赛分钟
                            data.get("b", ""),  # 比分
                            data.get("c", ""),  # 大球赔率
                            data.get("d", ""),  # 盘口
                            data.get("e", ""),  # 小球赔率
                            data.get("matchState", ""),  # 状态
                            data.get("isFeng2", ""),  # 是否封盘
                            data.get("changeTimeStr", "")  # 变化时间
                        ]
                    ws.append(row)
                    
                # 调整列宽
                for column in ws.columns:
                    max_length = 0
                    column = list(column)
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = (max_length + 2)
                    ws.column_dimensions[column[0].column_letter].width = adjusted_width
            
            # 保存工作簿
            file_name = f"分割导出_{odds_type}_{company}_{file_index+1}_{start_idx+1}_到_{end_idx}.xlsx"
            file_path = os.path.join(export_dir, file_name)
            wb.save(file_path)
            self.log_message(f"成功导出第{file_index+1}个文件: {file_name}", 'success', True)
            
        self.log_message(f"分割导出完成，共生成{file_count}个文件", 'success', True)
    
    def _create_odds_sheet_with_company(self, workbook, sheet_info, data_source):
        """创建盘口数据工作表（带公司信息）"""
        match_id = sheet_info['match_id']
        home_team = sheet_info['home_team']
        away_team = sheet_info['away_team']
        odds_type = sheet_info['odds_type']
        company = sheet_info.get('company', 'HG')  # 获取公司信息，默认为HG
        
        # 设置工作表标题
        sheet_title = f"（{odds_type}-{company}）{home_team}-{away_team}"
        # 确保sheet名称不超过31个字符（Excel限制）
        if len(sheet_title) > 31:
            sheet_title = sheet_title[:28] + "..."
        
        # 避免重复名称
        base_title = sheet_title
        suffix = 1
        while sheet_title in workbook.sheetnames:
            sheet_title = f"{base_title}_{suffix}"
            suffix += 1
        
        # 创建工作表
        ws = workbook.create_sheet(title=sheet_title)
        
        # 根据盘口类型设置表头
        headers_map = {
            "亚盘": ["比赛ID", "数据类型", "比赛分钟", "比分", "主队赔率", "盘口", "客队赔率",
                    "状态", "是否封盘", "变化时间"],
            "欧盘": ["比赛ID", "数据类型",  "比赛分钟", "比分", "主胜赔率", "平局", "主负赔率",
                    "状态", "是否封盘", "变化时间"],
            "大小盘": ["比赛ID", "数据类型",  "比赛分钟", "比分", "大球赔率", "盘口", "小球赔率",
                      "状态", "是否封盘", "变化时间"]
        }
        headers = headers_map.get(odds_type, headers_map["亚盘"])
        ws.append(headers)
        
        # 获取该比赛该盘口类型的所有数据
        sheet_data = []
        for item in data_source[odds_type]:
            if str(item.get('matchId')) == match_id:
                sheet_data.append(item)
        
        # 按时间排序数据
        sorted_data = sorted(sheet_data, key=lambda x: x.get('createTime', 0))
        
        # 数据类型中文名称映射
        type_names = {
            'roll': '走地盘',
            'index': '即时盘',
            'breakfast': '早盘'
        }
        
        # 写入数据
        for data in sorted_data:
            # 根据盘口类型处理数据
            if odds_type == "亚盘":
                row = [
                    data.get("matchId", ""),
                    type_names.get(data.get("type", ""), ""),  # 数据类型
                    data.get("a", ""),  # 比赛分钟
                    data.get("b", ""),  # 比分
                    data.get("c", ""),  # 主队赔率
                    data.get("d", ""),  # 盘口
                    data.get("e", ""),  # 客队赔率
                    data.get("matchState", ""),  # 状态
                    data.get("isFeng2", ""),  # 是否封盘
                    data.get("changeTimeStr", "")  # 变化时间
                ]
            elif odds_type == "欧盘":
                row = [
                    data.get("matchId", ""),
                    type_names.get(data.get("type", ""), ""),  # 数据类型
                    data.get("a", ""),  # 比赛分钟
                    data.get("b", ""),  # 比分
                    data.get("c1", ""),  # 主胜赔率
                    data.get("c2", ""),  # 平局
                    data.get("c3", ""),  # 主负赔率
                    data.get("matchState", ""),  # 状态
                    data.get("isFeng2", ""),  # 是否封盘
                    data.get("changeTimeStr", "")  # 变化时间
                ]
            else:  # 大小盘
                row = [
                    data.get("matchId", ""),
                    type_names.get(data.get("type", ""), ""),  # 数据类型
                    data.get("a", ""),  # 比赛分钟
                    data.get("b", ""),  # 比分
                    data.get("c", ""),  # 大球赔率
                    data.get("d", ""),  # 盘口
                    data.get("e", ""),  # 小球赔率
                    data.get("matchState", ""),  # 状态
                    data.get("isFeng2", ""),  # 是否封盘
                    data.get("changeTimeStr", "")  # 变化时间
                ]
            ws.append(row)
        
        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column = list(column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column[0].column_letter].width = adjusted_width
            
        return ws

    def export_to_excel(self):
        import tkinter.filedialog as fd
        import json
        try:
            from openpyxl import Workbook
        except ImportError:
            self.log_message("未安装openpyxl库，请先安装：pip install openpyxl", 'error')
            return
        if not self.last_response_json:
            self.log_message("暂无可导出的响应数据", 'warning')
            return
        data = self.last_response_json
        # 选择保存路径
        file_path = fd.asksaveasfilename(defaultextension=".xlsx", filetypes=[("Excel文件", "*.xlsx")], title="保存Excel文件")
        if not file_path:
            return
        try:
            wb = Workbook()
            ws = wb.active
            ws.title = "比赛数据"
            # 数据清洗与导出
            # 兼容直接传入response.txt内容
            match_list = None
            if isinstance(data, dict) and 'data' in data and 'list' in data['data']:
                match_list = data['data']['list']
            elif isinstance(data, dict) and 'list' in data:
                match_list = data['list']
            elif isinstance(data, list):
                match_list = data
            else:
                self.log_message("响应数据格式不支持自动清洗导出", 'error')
                return
            # 设定需要导出的字段
            headers = [
                "序号","比赛ID","联赛", "主队", "客队", "主队得分", "客队得分", "开赛时间", "比赛状态","主队赔率","让球盘口", "客队赔率","大球赔率","大小球盘口","小球赔率"
            ]
            ws.append(headers)
            for idx, match in enumerate(match_list, 1):
                row = [
                    idx,
                    match.get("ID", ""),
                    match.get("league", ""),
                    match.get("home", ""),
                    match.get("away", ""),
                    match.get("homeScore", ""),
                    match.get("awayScore", ""),
                    match.get("time", ""),
                    match.get("stateDesc", ""),
                    match.get("HJSPL", ""), # 主队赔率
                    match.get("JSPKDesc", ""),
                    match.get("WJSPL", ""), # 客队赔率
                    match.get("DXQ_HJSPL", ""), # 大球赔率
                    match.get("DXQDesc", ""),
                    match.get("DXQ_WJSPL", ""), # 小球赔率         
     
                ]
                ws.append(row)
            wb.save(file_path)
            self.log_message(f"数据已成功导出到: {file_path}", 'success')
        except Exception as e:
            self.log_message(f"导出Excel失败: {str(e)}", 'error')

    def one_click_fetch(self):
        if not self.last_response_json:
            self.log_message("请先在主监听页面获取比赛数据", 'error', True)
            return
        # 新增：只抓取明细tab选中的比赛
        selected_items = self.detail_match_tree.selection() if hasattr(self, 'detail_match_tree') else []
        if selected_items:
            selected_match_ids = [self.detail_match_map[item_id] for item_id in selected_items if item_id in self.detail_match_map]
            if not selected_match_ids:
                self.log_message("请先选择要抓取的比赛", 'error', True)
                return
        else:
            selected_match_ids = None  # None表示全部比赛
        self.log_message("开始一键抓取所有盘口数据（线程池模式）...", 'info', True)
        self.one_click_fetch_btn.config(state=tk.DISABLED)
        self.stop_detail_btn.config(state=tk.NORMAL)
        self.all_odds_data = {"亚盘": [], "欧盘": [], "大小盘": []}
        self.detail_stop_event.clear()
        self.start_fetch_timer()
        threading.Thread(target=lambda: self._thread_pool_fetch(selected_match_ids), daemon=True).start()
    
    def _thread_pool_fetch(self, selected_match_ids=None):
        """使用线程池实现并发抓取"""
        try:
            # 获取线程池大小
            try:
                max_workers = int(self.thread_pool_size_var.get())
                if max_workers <= 0:
                    max_workers = 10  # 如果输入无效，使用默认值10（降低默认并发数）
            except ValueError:
                max_workers = 10  # 转换出错，使用默认值10
                self.log_message(f"线程池大小设置无效，使用默认值10", 'warning', True)
                
            self.thread_pool_size_var.set(str(max_workers))  # 更新UI显示
            
            # 获取比赛列表
            match_list = []
            if isinstance(self.last_response_json, dict):
                if 'data' in self.last_response_json and 'list' in self.last_response_json['data']:
                    match_list = self.last_response_json['data']['list']
                elif 'list' in self.last_response_json:
                    match_list = self.last_response_json['list']
            elif isinstance(self.last_response_json, list):
                match_list = self.last_response_json

            if not match_list:
                self.log_message("未找到可用的比赛数据", 'error', True)
                return

            # 新增：只抓取选中的比赛
            if selected_match_ids is not None:
                valid_matches = [match for match in match_list if match.get('ID') and str(match.get('ID')) in selected_match_ids]
            else:
                valid_matches = [match for match in match_list if match.get('ID')]
            total_matches = len(valid_matches)
            
            if total_matches == 0:
                self.log_message("未找到有效的比赛数据", 'error', True)
                return
                
            # 创建任务列表
            tasks = []
            
            # 添加所有盘口类型的所有比赛任务
            for odds_type in ["亚盘", "欧盘", "大小盘"]:
                # 根据盘口类型获取API
                detail_api = self.detail_api_urls[odds_type]
                
                for match in valid_matches:
                    match_id = match.get('ID')
                    if match_id:
                        # 创建任务信息
                        task = {
                            'odds_type': odds_type,
                            'match_id': match_id,
                            'match': match
                        }
                        tasks.append(task)
            
            # 线程安全的计数器
            task_counter = {'total': len(tasks), 'completed': 0}
            
            # 分批处理任务
            # 计算批次大小：最多同时处理线程池大小的3倍任务数
            batch_size = max_workers * 3
            total_batches = (len(tasks) + batch_size - 1) // batch_size
            
            self.log_message(f"线程池模式启动，线程数：{max_workers}，总任务数：{task_counter['total']}，分{total_batches}批处理", 'info', True)
            
            # 创建线程安全的结果字典
            results = {}
            for odds_type in ["亚盘", "欧盘", "大小盘"]:
                results[odds_type] = []
            
            # 按批次处理任务
            import random
            
            for batch_idx in range(total_batches):
                # 如果用户中止了抓取，则跳出循环
                if self.detail_stop_event.is_set():
                    break
                    
                # 计算当前批次的任务范围
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, len(tasks))
                batch_tasks = tasks[start_idx:end_idx]
                
                # 添加批次间的随机延迟，避免连续批次之间的请求过于密集
                if batch_idx > 0:
                    batch_delay = 2 + random.uniform(0, 3)  # 2-5秒的随机延迟
                    self.log_message(f"等待{batch_delay:.1f}秒后开始下一批次任务...", 'info', True)
                    time.sleep(batch_delay)
                
                self.log_message(f"开始处理第{batch_idx+1}/{total_batches}批任务，本批次任务数：{len(batch_tasks)}", 'info', True)
                
                # 线程池执行当前批次任务
                with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                    # 提交当前批次的所有任务
                    future_to_task = {
                        executor.submit(
                            self._fetch_single_match_odds, 
                            task, 
                            results, 
                            task_counter
                        ): task for task in batch_tasks
                    }
                    
                    # 处理已完成的任务
                    for future in concurrent.futures.as_completed(future_to_task):
                        if self.detail_stop_event.is_set():
                            # 取消所有未完成的任务
                            for f in future_to_task:
                                if not f.done():
                                    f.cancel()
                            break
                
                # 批次完成后，检查是否需要中止
                if self.detail_stop_event.is_set():
                    self.log_message(f"用户中止了抓取，已完成{batch_idx+1}/{total_batches}批次", 'warning', True)
                    break
                
                self.log_message(f"第{batch_idx+1}/{total_batches}批任务已完成", 'success', True)
            
            # 如果用户没有中止，则更新全局数据和导出
            if not self.detail_stop_event.is_set():
                # 更新全局数据集合
                for odds_type, data in results.items():
                    self.all_odds_data[odds_type] = data
                
                self.log_message(f"所有盘口数据抓取完成，共完成{task_counter['completed']}/{task_counter['total']}个任务", 'success', True)
                self.log_message("开始自动导出数据...", 'info', True)
                # 导出所有数据到Excel
                self.export_all_odds_data()
            else:
                self.log_message("用户中止了数据抓取操作", 'warning', True)
                
        except Exception as e:
            self.log_message(f"线程池抓取过程发生异常: {str(e)}", 'error', True)
        finally:
            # 停止抓取计时器
            self.stop_fetch_timer()
            
            # 更新界面状态
            self.root.after(0, lambda: self.one_click_fetch_btn.config(state=tk.NORMAL))
            self.root.after(0, lambda: self.stop_detail_btn.config(state=tk.DISABLED))
    
    def _fetch_single_match_odds(self, task, results, task_counter):
        """抓取单场比赛单种盘口类型数据的线程函数"""
        if self.detail_stop_event.is_set():
            return
            
        odds_type = task['odds_type']
        match_id = task['match_id']
        detail_api = self.detail_api_urls[odds_type]  # 使用常量API地址
        match = task['match']
        
        try:
            import json, hashlib, time, requests, random
            from datetime import datetime
            
            # 设置进度信息 - 修改为更友好的格式
            league = match.get('league', '')
            home_team = match.get('home', '')
            away_team = match.get('away', '')
            match_name = f"{league}:{home_team}\t-{away_team}"
            progress_info = f"【{odds_type}】【{match_name}】"
            self.log_message(f"正在抓取 {progress_info} 数据...", 'info', True)
            
            # 获取公司ID
            company = self.company_var.get()
            company_id = self.company_id_map.get(company, "3")  # 默认为HG(3)
            
            # 添加随机延时，避免同时发送大量请求
            try:
                interval = float(self.detail_interval_var.get())
                # 确保最小延时为0.5秒
                if interval < 0.5:
                    interval = 0.5
            except ValueError:
                interval = 2.0  # 默认延时2秒
                
            # 添加随机延时因子 (0.5 ~ 1.5 * interval)
            delay = interval * (0.5 + random.random())
            time.sleep(delay)
            
            # 准备请求参数
            current_time_millis = int(round(time.time() * 1000))
            
            # 根据盘口类型确定请求参数
            params = {
                "matchId": match_id,
                "companyId": company_id,
                "timeInMillis": current_time_millis,
                "timeZone": "GMT+0800",
                "platform": "web"
            }
            
            # 欧盘请求需要额外的type参数
            if odds_type == "欧盘":
                params["type"] = 2
            
            # 生成sign参数
            def generate_sign(params):
                # 删除已有的sign参数
                if 'sign' in params:
                    del params['sign']
                
                # 复制参数字典
                params_copy = params.copy()
                
                # 删除空值、"{}"或其他空对象
                for key in list(params_copy.keys()):
                    value = params_copy[key]
                    # 检查值是否为空 (None, "", [], {})
                    if not value and value != 0:
                        del params_copy[key]
                    # 检查值是否为"{}"
                    elif json.dumps(value) == "{}":
                        del params_copy[key]
                
                # 按键名排序
                sorted_keys = sorted(params_copy.keys())
                
                # 构建签名字符串
                sign_str = ""
                for key in sorted_keys:
                    sign_str += str(params_copy[key]) + "&"
                
                # 添加密钥并计算MD5
                sign_str += "FE1C636C5A855EA4"
                
                # 计算MD5并转为大写
                sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest().upper()
                
                return sign
            
            # 添加sign参数
            params['sign'] = generate_sign(params)
            
            # 构建完整的请求体
            request_body = {"params": params}
            
            # 设置请求头
            headers = {
                'Host': 'www.zqcf918.com',
                'Cookie': 'aliyungf_tc=6d3f706181aefebab22188e690a5e8af00edd503429d1c86a9a8e3d72c4fec8e; Secure=1',
                'Accept': 'application/json',
                'Content-Type': 'application/json;charset=UTF-8',
                'Sec-Ch-Ua-Mobile': '?0',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.6167.85 Safari/537.36',
                'Sec-Ch-Ua-Platform': 'Windows',
                'Origin': 'https://www.zqcf918.com',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty',
                'Referer': 'https://www.zqcf918.com/',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Priority': 'u=1, i',
            }
            
            # 添加重试机制
            max_retries = 3
            retry_delay = 2  # 初始重试延迟（秒）
            
            for retry_count in range(max_retries):
                try:
                    # 发送请求，添加超时设置
                    self.log_message(f"正在发送请求（第{retry_count+1}次尝试）...", 'info')
                    response = requests.post(
                        detail_api, 
                        json=request_body, 
                        headers=headers, 
                        timeout=(10, 30)  # 连接超时10秒，读取超时30秒
                    )
                    
                    # 请求成功，跳出重试循环
                    break
                except (requests.exceptions.ConnectionError, requests.exceptions.ReadTimeout) as e:
                    if retry_count < max_retries - 1:
                        # 计算指数退避重试延迟
                        current_retry_delay = retry_delay * (2 ** retry_count) + random.uniform(0, 1)
                        # 优化日志显示格式：显示盘口类型和赛事信息
                        self.log_message(f"获取{progress_info}失败，{retry_count+1}/{max_retries}次尝试，将在{current_retry_delay:.1f}秒后重试: {str(e)}", 'warning', True)
                        time.sleep(current_retry_delay)
                    else:
                        # 最后一次尝试也失败
                        raise
            
            # 处理响应
            if response.status_code == 200:
                api_data = response.json()
                
                # 日志文件生成功能已禁用
                # if task_counter['completed'] < 3:
                #     save_dir = os.path.join(os.getcwd(), "packet_logs")
                #     os.makedirs(save_dir, exist_ok=True)
                #     timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                #     packet_save_file = os.path.join(save_dir, f"api_detail_{odds_type}_{match_id}_{timestamp}.txt")
                #     
                #     with open(packet_save_file, 'w', encoding='utf-8') as f:
                #         f.write(f"请求时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                #         f.write(f"请求URL: {detail_api}\n")
                #         f.write(f"请求参数: {json.dumps(request_body, ensure_ascii=False)}\n")
                #         f.write(f"响应状态码: {response.status_code}\n")
                #         f.write(f"响应内容: {json.dumps(api_data, ensure_ascii=False, indent=2)}\n")
                
                # 处理获取到的数据
                if api_data and isinstance(api_data, dict) and 'data' in api_data:
                    # 处理盘口数据
                    data_types = {
                        'rollList': 'roll',  # 走地盘
                        'indexList': 'index',  # 即时盘
                        'breakfastList': 'breakfast'  # 早盘
                    }
                    
                    data_count = 0
                    for list_key, type_name in data_types.items():
                        if list_key in api_data['data']:
                            data_list = api_data['data'][list_key]
                            for item in data_list:
                                item['matchId'] = match_id
                                item['type'] = type_name
                                item['odds_type'] = odds_type  # 添加盘口类型标识
                                # 添加比赛信息
                                item['home_team'] = match.get('home', '')
                                item['away_team'] = match.get('away', '')
                            
                            # 线程安全地添加到结果集
                            with threading.Lock():
                                results[odds_type].extend(data_list)
                                data_count += len(data_list)
                
                self.log_message(f"成功获取 {progress_info} 数据，共{data_count}条记录", 'success', True)
            else:
                # 优化日志显示格式：显示盘口类型和赛事信息
                self.log_message(f"获取{progress_info}失败，状态码: {response.status_code}", 'error', True)
                
        except Exception as e:
            # 优化日志显示格式：显示盘口类型和赛事信息
            self.log_message(f"抓取{progress_info}数据时发生错误: {str(e)}", 'error', True)
        finally:
            # 更新任务计数
            with threading.Lock():
                task_counter['completed'] += 1
                completed = task_counter['completed']
                total = task_counter['total']
                
            # 更新总进度
            if completed % 10 == 0 or completed == total:  # 每10个任务或最后一个任务显示一次总进度
                self.log_message(f"总进度: {completed}/{total} ({completed/total*100:.1f}%)", 'info', True)

    def export_all_odds_data(self):
        """导出所有盘口类型的数据到一个Excel文件，按盘口类型合并所有场次数据"""
        import os
        from datetime import datetime
        
        # 检查是否有数据
        all_data_empty = True
        for odds_type, data_list in self.all_odds_data.items():
            if data_list:
                all_data_empty = False
                break
                
        if all_data_empty:
            self.log_message("暂无可导出的数据", 'warning', True)
            return
            
        try:
            from openpyxl import Workbook
        except ImportError:
            self.log_message("未安装openpyxl库，请先安装：pip install openpyxl", 'error', True)
            return

        # 获取当前公司信息
        company = self.company_var.get()
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_path = os.path.join(os.getcwd(), f"all_odds_data_{company}_{timestamp}.xlsx")

        try:
            wb = Workbook()
            
            # 首先创建一个包含主页面"导出数据"按钮数据的sheet
            first_sheet = wb.active
            first_sheet.title = "比赛数据"
            
            # 设定需要导出的字段（与export_to_excel方法保持一致）
            headers = [
                "序号","比赛ID","联赛", "主队", "客队", "主队得分", "客队得分", "开赛时间", "比赛状态",
                "主队赔率","让球盘口", "客队赔率","大球赔率","大小球盘口","小球赔率"
            ]
            first_sheet.append(headers)
            
            # 填充主页面数据
            match_list = None
            if isinstance(self.last_response_json, dict) and 'data' in self.last_response_json and 'list' in self.last_response_json['data']:
                match_list = self.last_response_json['data']['list']
            elif isinstance(self.last_response_json, dict) and 'list' in self.last_response_json:
                match_list = self.last_response_json['list']
            elif isinstance(self.last_response_json, list):
                match_list = self.last_response_json
                
            if match_list:
                for idx, match in enumerate(match_list, 1):
                    row = [
                        idx,
                        match.get("ID", ""),
                        match.get("league", ""),
                        match.get("home", ""),
                        match.get("away", ""),
                        match.get("homeScore", ""),
                        match.get("awayScore", ""),
                        match.get("time", ""),
                        match.get("stateDesc", ""),
                        match.get("HJSPL", ""), # 主队赔率
                        match.get("JSPKDesc", ""),
                        match.get("WJSPL", ""), # 客队赔率
                        match.get("DXQ_HJSPL", ""), # 大球赔率
                        match.get("DXQDesc", ""),
                        match.get("DXQ_WJSPL", ""), # 小球赔率         
                    ]
                    first_sheet.append(row)
            
            # 调整列宽
            for column in first_sheet.columns:
                max_length = 0
                column = list(column)
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2)
                first_sheet.column_dimensions[column[0].column_letter].width = adjusted_width
            
            # 创建三个按盘口类型合并的工作表
            odds_types = ["亚盘", "欧盘", "大小盘"]
            
            # 为每种盘口类型创建一个工作表
            for odds_type in odds_types:
                ws = wb.create_sheet(title=f"{odds_type}数据")
                
                # 根据盘口类型设置表头
                headers_map = {
                    "亚盘": ["比赛ID", "主队", "客队", "数据类型", "比赛分钟", "比分", "主队赔率", "盘口", "客队赔率",
                            "状态", "是否封盘", "变化时间"],
                    "欧盘": ["比赛ID", "主队", "客队", "数据类型", "比赛分钟", "比分", "主胜赔率", "平局", "主负赔率",
                            "状态", "是否封盘", "变化时间"],
                    "大小盘": ["比赛ID", "主队", "客队", "数据类型", "比赛分钟", "比分", "大球赔率", "盘口", "小球赔率",
                             "状态", "是否封盘", "变化时间"]
                }
                headers = headers_map.get(odds_type, headers_map["亚盘"])
                ws.append(headers)
                
                # 获取该盘口类型的所有数据
                all_data = self.all_odds_data[odds_type]
                
                # 按比赛ID和时间排序数据
                sorted_data = sorted(all_data, key=lambda x: (str(x.get('matchId', '')), x.get('createTime', 0)))
                
                # 数据类型中文名称映射
                type_names = {
                    'roll': '走地盘',
                    'index': '即时盘',
                    'breakfast': '早盘'
                }
                
                # 写入数据，并将赔率和盘口转换为数字类型
                for data in sorted_data:
                    match_id = data.get("matchId", "")
                    home_team = data.get("home_team", "")
                    away_team = data.get("away_team", "")
                    
                    # 根据盘口类型处理数据
                    if odds_type == "亚盘":
                        # 转换为数字类型
                        try:
                            c_value = float(data.get("c", "0")) if data.get("c") else 0
                        except (ValueError, TypeError):
                            c_value = 0
                            
                        try:
                            d_value = float(data.get("d", "0")) if data.get("d") else 0
                        except (ValueError, TypeError):
                            d_value = 0
                            
                        try:
                            e_value = float(data.get("e", "0")) if data.get("e") else 0
                        except (ValueError, TypeError):
                            e_value = 0
                        
                        row = [
                            match_id,
                            home_team,
                            away_team,
                            type_names.get(data.get("type", ""), ""),  # 数据类型
                            data.get("a", ""),  # 比赛分钟
                            data.get("b", ""),  # 比分
                            c_value,  # 主队赔率 (数字)
                            d_value,  # 盘口 (数字)
                            e_value,  # 客队赔率 (数字)
                            data.get("matchState", ""),  # 状态
                            data.get("isFeng2", ""),  # 是否封盘
                            data.get("changeTimeStr", "")  # 变化时间
                        ]
                    elif odds_type == "欧盘":
                        # 转换为数字类型
                        try:
                            c1_value = float(data.get("c1", "0")) if data.get("c1") else 0
                        except (ValueError, TypeError):
                            c1_value = 0
                            
                        try:
                            c2_value = float(data.get("c2", "0")) if data.get("c2") else 0
                        except (ValueError, TypeError):
                            c2_value = 0
                            
                        try:
                            c3_value = float(data.get("c3", "0")) if data.get("c3") else 0
                        except (ValueError, TypeError):
                            c3_value = 0
                        
                        row = [
                            match_id,
                            home_team,
                            away_team,
                            type_names.get(data.get("type", ""), ""),  # 数据类型
                            data.get("a", ""),  # 比赛分钟
                            data.get("b", ""),  # 比分
                            c1_value,  # 主胜赔率 (数字)
                            c2_value,  # 平局 (数字)
                            c3_value,  # 主负赔率 (数字)
                            data.get("matchState", ""),  # 状态
                            data.get("isFeng2", ""),  # 是否封盘
                            data.get("changeTimeStr", "")  # 变化时间
                        ]
                    else:  # 大小盘
                        # 转换为数字类型
                        try:
                            c_value = float(data.get("c", "0")) if data.get("c") else 0
                        except (ValueError, TypeError):
                            c_value = 0
                            
                        try:
                            d_value = float(data.get("d", "0")) if data.get("d") else 0
                        except (ValueError, TypeError):
                            d_value = 0
                            
                        try:
                            e_value = float(data.get("e", "0")) if data.get("e") else 0
                        except (ValueError, TypeError):
                            e_value = 0
                        
                        row = [
                            match_id,
                            home_team,
                            away_team,
                            type_names.get(data.get("type", ""), ""),  # 数据类型
                            data.get("a", ""),  # 比赛分钟
                            data.get("b", ""),  # 比分
                            c_value,  # 大球赔率 (数字)
                            d_value,  # 盘口 (数字)
                            e_value,  # 小球赔率 (数字)
                            data.get("matchState", ""),  # 状态
                            data.get("isFeng2", ""),  # 是否封盘
                            data.get("changeTimeStr", "")  # 变化时间
                        ]
                    ws.append(row)
                
                # 调整列宽
                for column in ws.columns:
                    max_length = 0
                    column = list(column)
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = (max_length + 2)
                    ws.column_dimensions[column[0].column_letter].width = adjusted_width

            wb.save(file_path)
            self.log_message(f"所有盘口数据已成功导出到: {file_path}", 'success', True)
        except Exception as e:
            self.log_message(f"导出所有盘口数据Excel失败: {str(e)}", 'error', True)

    def _create_odds_sheet(self, workbook, sheet_info, data_source):
        """创建盘口数据工作表"""
        match_id = sheet_info['match_id']
        home_team = sheet_info['home_team']
        away_team = sheet_info['away_team']
        odds_type = sheet_info['odds_type']
        
        # 设置工作表标题
        sheet_title = f"（{odds_type}）{home_team}-{away_team}"
        # 确保sheet名称不超过31个字符（Excel限制）
        if len(sheet_title) > 31:
            sheet_title = sheet_title[:28] + "..."
        
        # 避免重复名称
        base_title = sheet_title
        suffix = 1
        while sheet_title in workbook.sheetnames:
            sheet_title = f"{base_title}_{suffix}"
            suffix += 1
        
        # 创建工作表
        ws = workbook.create_sheet(title=sheet_title)
        
        # 根据盘口类型设置表头
        headers_map = {
            "亚盘": ["比赛ID", "数据类型",  "比赛分钟", "比分", "主队赔率", "盘口", "客队赔率",
                    "状态", "是否封盘", "变化时间"],
            "欧盘": ["比赛ID", "数据类型",  "比赛分钟", "比分", "主胜赔率", "平局", "主负赔率",
                    "状态", "是否封盘", "变化时间"],
            "大小盘": ["比赛ID", "数据类型",  "比赛分钟", "比分", "大球赔率", "盘口", "小球赔率",
                      "状态", "是否封盘", "变化时间"]
        }
        headers = headers_map.get(odds_type, headers_map["亚盘"])
        ws.append(headers)
        
        # 获取该比赛该盘口类型的所有数据
        sheet_data = []
        for item in data_source[odds_type]:
            if str(item.get('matchId')) == match_id:
                sheet_data.append(item)
        
        # 按时间排序数据
        sorted_data = sorted(sheet_data, key=lambda x: x.get('createTime', 0))
        
        # 数据类型中文名称映射
        type_names = {
            'roll': '走地盘',
            'index': '即时盘',
            'breakfast': '早盘'
        }
        
        # 写入数据
        for data in sorted_data:
            # 根据盘口类型处理数据
            if odds_type == "亚盘":
                row = [
                    data.get("matchId", ""),
                    type_names.get(data.get("type", ""), ""),  # 数据类型
                    data.get("a", ""),  # 比赛分钟
                    data.get("b", ""),  # 比分
                    data.get("c", ""),  # 主队赔率
                    data.get("d", ""),  # 盘口
                    data.get("e", ""),  # 客队赔率
                    data.get("matchState", ""),  # 状态
                    data.get("isFeng2", ""),  # 是否封盘
                    data.get("changeTimeStr", "")  # 变化时间
                ]
            elif odds_type == "欧盘":
                row = [
                    data.get("matchId", ""),
                    type_names.get(data.get("type", ""), ""),  # 数据类型
                    data.get("a", ""),  # 比赛分钟
                    data.get("b", ""),  # 比分
                    data.get("c1", ""),  # 主胜赔率
                    data.get("c2", ""),  # 平局
                    data.get("c3", ""),  # 主负赔率
                    data.get("matchState", ""),  # 状态
                    data.get("isFeng2", ""),  # 是否封盘
                    data.get("changeTimeStr", "")  # 变化时间
                ]
            else:  # 大小盘
                row = [
                    data.get("matchId", ""),
                    type_names.get(data.get("type", ""), ""),  # 数据类型
                    data.get("a", ""),  # 比赛分钟
                    data.get("b", ""),  # 比分
                    data.get("c", ""),  # 大球赔率
                    data.get("d", ""),  # 盘口
                    data.get("e", ""),  # 小球赔率
                    data.get("matchState", ""),  # 状态
                    data.get("isFeng2", ""),  # 是否封盘
                    data.get("changeTimeStr", "")  # 变化时间
                ]
            ws.append(row)
        
        # 调整列宽
        for column in ws.columns:
            max_length = 0
            column = list(column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column[0].column_letter].width = adjusted_width
            
        return ws

    def split_export_data(self):
        """分割导出数据功能(已废弃)"""
        self.log_message("分割导出功能已移除，请使用一键抓取后的导出功能", 'warning', True)
    
    # def _split_export_all_odds_data(self, export_dir, match_info_dict, matches_per_file):
    #     """分割导出一键抓取的所有盘口数据"""
    #     # 此函数已废弃
    #     pass
    
    # def _split_export_single_odds_data(self, export_dir, match_info_dict, matches_per_file):
    #     """分割导出单一盘口数据"""
    #     # 此函数已废弃
    #     pass

    def on_odds_type_changed(self, event=None):
        """盘口类型变更时更新"""
        odds_type = self.odds_type_var.get()
        company = self.company_var.get()
        company_id = self.company_id_map.get(company, "3")  # 默认为HG(3)
    
    def on_company_changed(self, event=None):
        """公司选择变更时更新API参数"""
        # 获取当前选择的公司和对应的ID
        company = self.company_var.get()
        company_id = self.company_id_map.get(company, "3")  # 默认为HG(3)
        
        self.log_message(f"已更新公司选择为{company}(companyId={company_id})", 'info', True)

    def start_fetch_timer(self):
        """启动抓取计时器"""
        self.fetch_start_time = time.time()
        self.fetch_timer_running = True
        self.update_fetch_duration()
        
    def stop_fetch_timer(self):
        """停止抓取计时器"""
        self.fetch_timer_running = False
        if self.fetch_timer_id:
            self.root.after_cancel(self.fetch_timer_id)
            self.fetch_timer_id = None
        
    def update_fetch_duration(self):
        """更新抓取持续时间显示"""
        if not self.fetch_timer_running:
            return
            
        if self.fetch_start_time:
            elapsed_seconds = int(time.time() - self.fetch_start_time)
            hours, remainder = divmod(elapsed_seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            duration_str = f"数据抓取已用时: {hours:02d}:{minutes:02d}:{seconds:02d}"
            self.fetch_duration_var.set(duration_str)
            
        # 每秒更新一次
        self.fetch_timer_id = self.root.after(1000, self.update_fetch_duration)

    def stop_detail_fetch(self):
        self.detail_stop_event.set()
        # 更新按钮状态
        self.stop_detail_btn.config(state=tk.DISABLED)
        self.one_click_fetch_btn.config(state=tk.NORMAL)
        
        # 停止抓取计时器
        self.stop_fetch_timer()
        
        self.log_message("正在停止明细数据抓取...", 'warning', True)

    def process_messages_now(self):
        """立即处理消息队列中的内容"""
        # 直接调用process_messages进行处理
        self.process_messages()

    def update_detail_log_buffer(self):
        """将缓冲区的日志批量更新到UI"""
        if not self.detail_log_buffer:
            return
            
        # 批量添加日志文本
        with threading.Lock():
            buffer_copy = self.detail_log_buffer.copy()
            self.detail_log_buffer.clear()
        
        # 检查文本框中的行数
        max_lines = 1000
        current_lines = int(self.detail_log_text.index('end-1c').split('.')[0])
        
        if current_lines > max_lines:
            # 如果日志行数超过上限，删除前面一半的日志
            self.detail_log_text.delete(1.0, f"{int(current_lines/2)}.0")
            self.detail_log_text.insert(1.0, "[...日志已截断以提高性能...]\n", 'warning')
        
        # 批量添加所有日志
        for msg_type, timestamp, message in buffer_copy:
            self.detail_log_text.insert(tk.END, f"{timestamp} - {message}\n", msg_type)
        
        # 仅在有实际更新时滚动到底部
        if buffer_copy:
            self.detail_log_text.see(tk.END)
        
        # 更新最后更新时间
        self.detail_log_last_update = time.time()

    def setup_ai_predict_ui(self, parent_frame):
        """设置AI预测tab的UI"""
        # 配置区
        config_frame = ttk.LabelFrame(parent_frame, text="AI配置")
        config_frame.pack(fill=tk.X, padx=5, pady=5)

        # 第一行配置 - AI模型选择和温度
        first_row = ttk.Frame(config_frame)
        first_row.pack(fill=tk.X, pady=2)
        
        ttk.Label(first_row, text="AI来源:").pack(side=tk.LEFT, padx=5)
        self.ai_source_var = tk.StringVar(value="SiliconFlow")
        ai_source_combo = ttk.Combobox(first_row, textvariable=self.ai_source_var, values=["DeepSeek", "SiliconFlow", "KIMI","Ollama", "OpenAI"], state="readonly", width=10)
        ai_source_combo.pack(side=tk.LEFT, padx=2)
        ai_source_combo.bind("<<ComboboxSelected>>", self.on_ai_source_changed)
        
        ttk.Label(first_row, text="模型名称:").pack(side=tk.LEFT, padx=5)
        self.ai_model_var = tk.StringVar(value="deepseek-ai/DeepSeek-V3")
        self.ai_model_combo = ttk.Combobox(first_row, textvariable=self.ai_model_var, values=[
            "deepseek-ai/DeepSeek-V3",
            "deepseek-ai/DeepSeek-R1",
            "Qwen/Qwen3-235B-A22B",            
            "deepseek-chat",
            "deepseek-reasoner",
            "qwen3:30b-a3b",
            "gemma3:12b",
            "qwen3:8b",
            "deepseek-llm:latest",
            "deepseek-r1:8b"
        ], width=20)
        self.ai_model_combo.pack(side=tk.LEFT, padx=2)
        self.ai_model_combo.configure(state="normal")  # 允许自定义输入
        
        # 将"温度"控件移到"模型名称"控件右侧
        ttk.Label(first_row, text="温度:").pack(side=tk.LEFT, padx=5)
        self.ai_temperature_var = tk.DoubleVar(value=0.7)
        ttk.Entry(first_row, textvariable=self.ai_temperature_var, width=5).pack(side=tk.LEFT, padx=2)
        
        # 第二行配置 - API配置
        second_row = ttk.Frame(config_frame)
        second_row.pack(fill=tk.X, pady=2)
        
        ttk.Label(second_row, text="API地址:").pack(side=tk.LEFT, padx=5)
        self.ai_api_url_var = tk.StringVar(value="https://api.siliconflow.cn/v1/chat/completions")
        self.ai_api_url_entry = ttk.Entry(second_row, textvariable=self.ai_api_url_var, width=40)
        self.ai_api_url_entry.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(second_row, text="API密钥:").pack(side=tk.LEFT, padx=5)
        self.ai_api_key_var = tk.StringVar(value="sk-pioxcfzyxfvuhblotyfmeywhzjaignjzwrnxqezzxnmbrasr")
        self.ai_api_key_entry = ttk.Entry(second_row, textvariable=self.ai_api_key_var, width=25, show="*")
        self.ai_api_key_entry.pack(side=tk.LEFT, padx=2)
        
        # 第三行配置 - 预测设置和System提示
        third_row = ttk.Frame(config_frame)
        third_row.pack(fill=tk.X, pady=2)
        
        ttk.Label(third_row, text="预测类型:").pack(side=tk.LEFT, padx=5)
        self.ai_predict_type_var = tk.StringVar(value="大小球")
        predict_type_combo = ttk.Combobox(third_row, textvariable=self.ai_predict_type_var, 
                                           values=["胜平负", "大小球", "让球", "综合分析"], state="readonly", width=10)
        predict_type_combo.pack(side=tk.LEFT, padx=2)
        
        ttk.Label(third_row, text="最大分析场次:").pack(side=tk.LEFT, padx=5)
        self.ai_max_matches_var = tk.IntVar(value=50)
        ttk.Entry(third_row, textvariable=self.ai_max_matches_var, width=5).pack(side=tk.LEFT, padx=2)
        
        # 将"System提示"控件移到"最大分析场次"控件右侧
        ttk.Label(third_row, text="System提示:").pack(side=tk.LEFT, padx=5)
        self.ai_system_prompt_var = tk.StringVar(value="你是一位专业的足球赛事分析师，精通赔率分析")
        ttk.Entry(third_row, textvariable=self.ai_system_prompt_var, width=40).pack(side=tk.LEFT, padx=2)
        
        # 比赛选择区
        match_frame = ttk.LabelFrame(parent_frame, text="比赛选择")
        match_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 比赛列表 - 替换为Treeview
        match_list_frame = ttk.Frame(match_frame)
        match_list_frame.pack(fill=tk.X, expand=True, pady=2)
        
        # 创建Treeview组件
        columns = ("序号", "联赛", "对阵", "开赛时间", "比赛状态", "比分", "让球盘口", "大小盘口")
        self.ai_match_tree = ttk.Treeview(match_list_frame, columns=columns, show='headings', height=8)
        
        # 设置列宽和表头
        self.ai_match_tree.column("序号", width=40, anchor="center")
        self.ai_match_tree.column("联赛", width=60, anchor="w")
        self.ai_match_tree.column("对阵", width=138, anchor="w")
        self.ai_match_tree.column("开赛时间", width=128, anchor="center")
        self.ai_match_tree.column("比赛状态", width=60, anchor="center")
        self.ai_match_tree.column("比分", width=60, anchor="center")
        self.ai_match_tree.column("让球盘口", width=68, anchor="center")
        self.ai_match_tree.column("大小盘口", width=68, anchor="center")
        
        self.ai_match_tree.heading("序号", text="序号")
        self.ai_match_tree.heading("联赛", text="联赛")
        self.ai_match_tree.heading("对阵", text="对阵")
        self.ai_match_tree.heading("开赛时间", text="开赛时间")
        self.ai_match_tree.heading("比赛状态", text="比赛状态") 
        self.ai_match_tree.heading("比分", text="比分")
        self.ai_match_tree.heading("让球盘口", text="让球盘口")
        self.ai_match_tree.heading("大小盘口", text="大小盘口")
        
        # 添加垂直滚动条
        tree_scrollbar = ttk.Scrollbar(match_list_frame, orient="vertical", command=self.ai_match_tree.yview)
        self.ai_match_tree.configure(yscrollcommand=tree_scrollbar.set)
        
        # 放置Treeview和滚动条
        self.ai_match_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 按钮区
        button_frame = ttk.Frame(match_frame)
        button_frame.pack(fill=tk.X, pady=2)
        
        ttk.Button(button_frame, text="刷新比赛", command=self.refresh_ai_matches).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="全选", command=self.select_all_ai_matches).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消选择", command=self.deselect_all_ai_matches).pack(side=tk.LEFT, padx=5)
        
        # 操作区
        operation_frame = ttk.LabelFrame(parent_frame, text="操作区")
        operation_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.start_ai_predict_btn = ttk.Button(operation_frame, text="开始AI预测", command=self.start_ai_predict)
        self.start_ai_predict_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        self.stop_ai_predict_btn = ttk.Button(operation_frame, text="停止预测", command=self.stop_ai_predict, state=tk.DISABLED)
        self.stop_ai_predict_btn.pack(side=tk.LEFT, padx=5, pady=5)
        
        ttk.Button(operation_frame, text="清空结果", command=self.clear_ai_predict_results).pack(side=tk.LEFT, padx=5, pady=5)
        
        # 添加AI预测计时器显示
        self.ai_predict_duration_var = tk.StringVar(value="AI预测已用时: 00:00:00")
        self.ai_predict_duration_label = ttk.Label(operation_frame, textvariable=self.ai_predict_duration_var, foreground="blue")
        self.ai_predict_duration_label.pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(operation_frame, text="导出预测", command=self.export_ai_predict_results).pack(side=tk.RIGHT, padx=5, pady=5)
        
        # 自动导出选项
        auto_export_frame = ttk.Frame(operation_frame)
        auto_export_frame.pack(side=tk.RIGHT, padx=5, pady=5)
        self.auto_export_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(auto_export_frame, text="自动导出", variable=self.auto_export_var).pack(side=tk.LEFT)
        
        # 结果区
        result_frame = ttk.LabelFrame(parent_frame, text="AI预测结果")
        result_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.ai_result_text = scrolledtext.ScrolledText(result_frame, wrap=tk.WORD)
        self.ai_result_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.ai_result_text.tag_config('title', foreground='blue', font=('Arial', 10, 'bold'))
        self.ai_result_text.tag_config('info', foreground='black')
        self.ai_result_text.tag_config('success', foreground='green')
        self.ai_result_text.tag_config('warning', foreground='orange')
        self.ai_result_text.tag_config('error', foreground='red')
        self.ai_result_text.tag_config('highlight', foreground='purple')
        
        # 初始化时触发AI来源变更事件，确保默认值正确设置
        self.on_ai_source_changed()

    def on_ai_source_changed(self, event=None):
        """处理AI来源变更事件"""
        source = self.ai_source_var.get()
        
        # 根据来源更新UI和默认值
        if source == "Ollama":
            self.ai_api_url_var.set("http://localhost:11434/api/generate")
            self.ai_model_var.set("deepseek-r1:8b")
            self.ai_api_key_entry.config(state=tk.DISABLED)
        elif source == "OpenAI":
            self.ai_api_url_var.set("https://api.openai.com/v1/chat/completions")
            self.ai_model_var.set("gpt-3.5-turbo")
            self.ai_api_key_entry.config(state=tk.NORMAL)
        elif source == "DeepSeek":
            self.ai_api_url_var.set("https://api.deepseek.com/v1/chat/completions")
            self.ai_model_var.set("deepseek-chat")
            self.ai_api_key_entry.config(state=tk.NORMAL)
        elif source == "KIMI":
            self.ai_api_url_var.set("https://api.moonshot.cn/v1/chat/completions")
            self.ai_model_var.set("moonshot-v1-8k")
            self.ai_api_key_entry.config(state=tk.NORMAL)
        else:  # SiliconFlow 或其他API
            self.ai_api_url_var.set("https://api.siliconflow.cn/v1/chat/completions")
            self.ai_model_var.set("deepseek-ai/DeepSeek-V3")
            self.ai_api_key_entry.config(state=tk.NORMAL)
            
        self.ai_result_text.insert(tk.END, f"已切换AI来源到: {source}\n", 'info')
    
    def refresh_ai_matches(self):
        """刷新可供AI分析的比赛列表"""
        # 清空当前列表
        self.ai_match_tree.delete(*self.ai_match_tree.get_children())
        
        # 获取比赛数据
        match_data = []
        
        # 从last_response_json中获取比赛列表
        if hasattr(self, 'last_response_json') and self.last_response_json:
            if isinstance(self.last_response_json, dict):
                if 'data' in self.last_response_json and 'list' in self.last_response_json['data']:
                    match_data = self.last_response_json['data']['list']
                elif 'list' in self.last_response_json:
                    match_data = self.last_response_json['list']
            elif isinstance(self.last_response_json, list):
                match_data = self.last_response_json
        
        # 如果主页面比赛列表中有数据，使用主页面中的数据
        if hasattr(self, 'main_match_map') and self.main_match_map and hasattr(self, 'main_match_tree'):
            match_data = []
            for item_id in self.main_match_tree.get_children():
                values = self.main_match_tree.item(item_id, 'values')
                match_id = self.main_match_map.get(item_id)
                if match_id and len(values) >= 8:  # 更新为8个字段
                    # 获取比赛信息
                    league = values[1]
                    teams = values[2].split(" VS ")
                    home_team = teams[0] if len(teams) > 0 else ""
                    away_team = teams[1] if len(teams) > 1 else ""
                    time_str = values[3]
                    state = values[4]
                    score = values[5]
                    handicap = values[6]
                    ou_handicap = values[7]
                    
                    # 解析比分
                    scores = score.split("-")
                    home_score = scores[0] if len(scores) > 0 else "0"
                    away_score = scores[1] if len(scores) > 1 else "0"
                    
                    # 构建比赛数据
                    match = {
                        'ID': match_id,
                        'home': home_team,
                        'away': away_team,
                        'league': league,
                        'time': time_str,
                        'stateDesc': state,
                        'homeScore': home_score,
                        'awayScore': away_score,
                        'JSPKDesc': handicap,
                        'DXQDesc': ou_handicap
                    }
                    match_data.append(match)
        
        # 如果没有比赛数据，尝试从已经抓取的明细数据中获取比赛信息
        if not match_data and hasattr(self, 'detail_data') and self.detail_data:
            match_ids = set()
            for data in self.detail_data:
                match_id = data.get('matchId')
                if match_id and data.get('home_team') and data.get('away_team'):
                    match_ids.add((match_id, data.get('home_team'), data.get('away_team')))
            
            # 转换为与主数据结构兼容的格式
            for match_id, home_team, away_team in match_ids:
                match_data.append({
                    'ID': match_id,
                    'home': home_team,
                    'away': away_team
                })
        
        # 更新列表框
        if match_data:
            # 保存比赛ID到信息的映射
            self.ai_match_map = {}

            # 获取当前筛选设置
            filter_mode = self.match_display_filter_var.get()

            # 准备数据源用于检查赔率数据
            data_source = self._prepare_ai_data_source()
            valid_match_count = 0
            filtered_out_count = 0
            display_idx = 1  # 用于显示的序号

            for idx, match in enumerate(match_data, 1):
                match_id = match.get('ID', '')
                home_team = match.get('home', '')
                away_team = match.get('away', '')
                league = match.get('league', '')
                time_str = match.get('time', '')
                state = match.get('stateDesc', '')
                home_score = match.get('homeScore', '0')
                away_score = match.get('awayScore', '0')
                score = f"{home_score}-{away_score}"

                if match_id and home_team and away_team:
                    # 获取让球盘口信息
                    handicap = match.get("JSPKDesc", "")
                    # 获取大小球盘口信息
                    ou_handicap = match.get("DXQDesc", "")

                    # 应用与主页面相同的筛选逻辑
                    should_display = True
                    if filter_mode == "只显示有数据赛事":
                        # 检查让球盘口和大小球盘口是否都为空或"-"
                        if (not handicap or handicap == "-") and (not ou_handicap or ou_handicap == "-"):
                            should_display = False

                    # 只有通过筛选的比赛才进行后续处理
                    if should_display:
                        # 检查该比赛是否有赔率数据
                        has_odds_data = False

                        # 如果detail_data为空，则直接添加比赛（因为可能还没有抓取过明细数据）
                        if not hasattr(self, 'detail_data') or not self.detail_data:
                            has_odds_data = True
                        else:
                            # 对于每种赔率类型，检查是否有该比赛的数据
                            for odds_type, odds_data in data_source.items():
                                if any(str(data.get('matchId', '')) == str(match_id) for data in odds_data):
                                    has_odds_data = True
                                    break

                        # 只有有赔率数据的比赛才添加到列表
                        if has_odds_data:
                            display_text = f"{home_team} VS {away_team}"

                            item_id = self.ai_match_tree.insert('', 'end', values=(display_idx, league, display_text, time_str, state, score, handicap, ou_handicap))
                            # 将match_id与item_id建立映射关系
                            self.ai_match_map[item_id] = match_id
                            valid_match_count += 1
                            display_idx += 1
                        else:
                            filtered_out_count += 1
                    else:
                        filtered_out_count += 1
            
            if valid_match_count > 0:
                self.ai_result_text.insert(tk.END, f"已加载 {valid_match_count} 场有效比赛", 'info')
                if filtered_out_count > 0:
                    self.ai_result_text.insert(tk.END, f"（已过滤 {filtered_out_count} 场无盘赔数据的比赛）\n", 'warning')
                else:
                    self.ai_result_text.insert(tk.END, "\n", 'info')
            else:
                self.ai_result_text.insert(tk.END, "未找到有有效盘赔数据的比赛\n", 'warning')
        else:
            self.ai_result_text.insert(tk.END, "未找到可用的比赛数据，请先在主界面抓取数据\n", 'warning')
    
    def select_all_ai_matches(self):
        """选择所有比赛"""
        for item in self.ai_match_tree.get_children():
            self.ai_match_tree.selection_add(item)
    
    def deselect_all_ai_matches(self):
        """取消选择所有比赛"""
        self.ai_match_tree.selection_remove(*self.ai_match_tree.get_children())
    
    def start_ai_predict(self):
        """开始AI预测"""
        # 检查是否有比赛被选中
        selected_items = self.ai_match_tree.selection()
        if not selected_items:
            self.ai_result_text.insert(tk.END, "请先选择要分析的比赛\n", 'error')
            return
        
        # 检查是否有数据源
        if not hasattr(self, 'detail_data') or not self.detail_data:
            if not hasattr(self, 'all_odds_data') or not any(self.all_odds_data.values()):
                self.ai_result_text.insert(tk.END, "没有可用的数据源，请先抓取数据\n", 'error')
                return
        
        # 获取选中的比赛
        selected_matches = []
        for item_id in selected_items:
            match_id = self.ai_match_map.get(item_id)
            if match_id:
                # 获取比赛信息用于显示
                item_values = self.ai_match_tree.item(item_id, 'values')
                match_name = item_values[2] if len(item_values) > 2 else f"比赛ID:{match_id}"
                selected_matches.append((match_id, match_name))
        
        # 更新UI状态
        self.ai_result_text.delete(1.0, tk.END)
        self.ai_result_text.insert(tk.END, f"开始分析 {len(selected_matches)} 场比赛...\n\n", 'title')
        self.start_ai_predict_btn.config(state=tk.DISABLED)
        self.stop_ai_predict_btn.config(state=tk.NORMAL)
        
        # 设置标志位
        self.ai_predicting = True
        
        # 启动AI预测计时器
        self.start_ai_predict_timer()
        
        # 启动预测线程
        self.ai_predict_thread = threading.Thread(
            target=self._ai_predict_thread, 
            args=(selected_matches,), 
            daemon=True
        )
        self.ai_predict_thread.start()
    
    def stop_ai_predict(self):
        """停止AI预测"""
        self.ai_predicting = False
        self.ai_result_text.insert(tk.END, "正在停止预测...\n", 'warning')
        self.stop_ai_predict_btn.config(state=tk.DISABLED)
        
        # 停止AI预测计时器
        self.stop_ai_predict_timer()
        
    def clear_ai_predict_results(self):
        """清空AI预测结果"""
        self.ai_result_text.delete(1.0, tk.END)
        self.ai_result_text.insert(tk.END, "已清空预测结果\n", 'info')
        
        # 重置AI预测计时器
        self.stop_ai_predict_timer()
        self.ai_predict_duration_var.set("AI预测已用时: 00:00:00")
    
    def export_ai_predict_results(self):
        """导出AI预测结果到文件"""
        # 检查是否有结果可导出
        content = self.ai_result_text.get(1.0, tk.END).strip()
        if not content:
            self.ai_result_text.insert(tk.END, "没有可导出的结果\n", 'error')
            return
        
        try:
            import tkinter.filedialog as fd
            from datetime import datetime
            import os
            # 获取当前时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            predict_type = self.ai_predict_type_var.get()
            ai_source = self.ai_source_var.get()  # 获取AI来源
            model_name = self.ai_model_var.get()
            # 替换model_name中的非法文件名字符
            safe_model_name = re.sub(r'[\\/:*?"<>|]', '-', model_name)
            # 询问保存路径
            file_types = [
                ("HTML文件", "*.html"),
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ]
            file_path = fd.asksaveasfilename(
                defaultextension=".html",
                filetypes=file_types,
                title="保存AI预测结果",
                initialfile=f"AI预测_{predict_type}_{ai_source}_{safe_model_name}_{timestamp}.html"
            )
            if not file_path:
                return  # 用户取消了保存
            # 根据文件扩展名决定保存格式
            _, ext = os.path.splitext(file_path)
            if ext.lower() == '.html':
                # 转换为HTML并保存
                html_content = self.markdown_to_html(content)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)
            else:
                # 保存为纯文本
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
            self.ai_result_text.insert(tk.END, f"\n结果已保存到: {file_path}\n", 'success')
        except Exception as e:
            self.ai_result_text.insert(tk.END, f"\n导出失败: {str(e)}\n", 'error')
    
    def markdown_to_html(self, markdown_text):
        """将Markdown文本转换为HTML"""
        try:
            # 尝试导入markdown库
            import markdown
            return markdown.markdown(markdown_text)
        except ImportError:
            # 如果没有安装markdown库，使用简单的替换方式
            html = markdown_text
            
            # 替换标题
            import re
            html = re.sub(r'^# (.*?)$', r'<h1>\1</h1>', html, flags=re.MULTILINE)
            html = re.sub(r'^## (.*?)$', r'<h2>\1</h2>', html, flags=re.MULTILINE)
            html = re.sub(r'^### (.*?)$', r'<h3>\1</h3>', html, flags=re.MULTILINE)
            
            # 替换粗体和斜体
            html = re.sub(r'\*\*(.*?)\*\*', r'<strong>\1</strong>', html)
            html = re.sub(r'\*(.*?)\*', r'<em>\1</em>', html)
            
            # 替换列表
            html = re.sub(r'^- (.*?)$', r'<li>\1</li>', html, flags=re.MULTILINE)
            
            # 替换换行
            html = html.replace('\n\n', '<br><br>')
            
            # 增加基本HTML结构
            html = f"""<!DOCTYPE html>
<html>
<head>
    <title>AI足球赛事分析结果</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; margin: 20px; }}
        h1 {{ color: #2c3e50; }}
        h2 {{ color: #3498db; border-bottom: 1px solid #eee; padding-bottom: 5px; }}
        h3 {{ color: #2980b9; }}
        .match {{ background-color: #f9f9f9; padding: 15px; margin-bottom: 20px; border-left: 5px solid #3498db; }}
        .result {{ background-color: #e8f4f8; padding: 10px; }}
        .highlight {{ color: #e74c3c; font-weight: bold; }}
    </style>
</head>
<body>
    <h1>AI足球赛事分析结果</h1>
    {html}
</body>
</html>"""
            
            return html
            
    def _ai_predict_thread(self, selected_matches):
        """AI预测执行线程"""
        try:
            # 保存当前的选中状态，确保在预测完成后能够正确导出数据
            current_selected_items = self.ai_match_tree.selection()
            
            # 获取AI配置
            ai_source = self.ai_source_var.get()
            model_name = self.ai_model_var.get()
            api_url = self.ai_api_url_var.get()
            api_key = self.ai_api_key_var.get()
            temperature = self.ai_temperature_var.get()
            system_prompt = self.ai_system_prompt_var.get()
            predict_type = self.ai_predict_type_var.get()
            max_matches = self.ai_max_matches_var.get()
            
            # 限制处理的比赛数量
            if len(selected_matches) > max_matches:
                selected_matches = selected_matches[:max_matches]
                self._update_ai_result(f"限制为前 {max_matches} 场比赛进行分析\n", 'warning')
            
            # 收集数据来源
            data_source = self._prepare_ai_data_source()
            
            # 准备收集所有预测结果，用于最后导出
            all_results = []
            
            # 逐个处理比赛
            for i, (match_id, match_name) in enumerate(selected_matches):
                if not self.ai_predicting:
                    self._update_ai_result("预测已被用户中止\n", 'warning')
                    break
                
                match_title = f"【{i+1}/{len(selected_matches)}】正在分析: {match_name}"
                self._update_ai_result(match_title + "\n", 'title')
                all_results.append(f"## {match_title}")
                
                # 收集该比赛的所有数据
                match_data = self._collect_match_data_for_ai(match_id, data_source)
                
                if not match_data:
                    error_msg = f"未找到比赛ID: {match_id} 的有效数据\n\n"
                    self._update_ai_result(error_msg, 'error')
                    all_results.append(error_msg)
                    continue
                
                # 构建提示词
                prompt = self._build_ai_prompt(match_id, match_name, match_data, predict_type)
                
                # 调用AI模型
                result = self._call_ai_model(ai_source, model_name, api_url, api_key, system_prompt, prompt, temperature)
                
                # 显示结果
                if result:
                    result_text = f"AI预测结果:\n{result}\n\n"
                    self._update_ai_result(result_text, 'info')
                    all_results.append(result_text)
                else:
                    error_msg = "AI分析失败，未能获取有效结果\n\n"
                    self._update_ai_result(error_msg, 'error')
                    all_results.append(error_msg)
            
            # 预测完成
            completion_msg = "所有比赛分析完成\n"
            self._update_ai_result(completion_msg, 'success')
            all_results.append(completion_msg)
            
            # 确保选中状态与预测前一致，以便正确提取数据
            self.ai_match_tree.selection_set(current_selected_items)
            
            # 检查是否需要自动导出
            if self.auto_export_var.get():
                self._auto_export_results("\n".join(all_results))
            
        except Exception as e:
            self._update_ai_result(f"预测过程中发生错误: {str(e)}\n", 'error')
        finally:
            # 恢复UI状态
            self.root.after(0, lambda: self.start_ai_predict_btn.config(state=tk.NORMAL))
            self.root.after(0, lambda: self.stop_ai_predict_btn.config(state=tk.DISABLED))
            self.ai_predicting = False
            
            # 停止AI预测计时器
            self.root.after(0, self.stop_ai_predict_timer)
            
    def _auto_export_results(self, markdown_content):
        """自动导出预测结果为HTML文件和Excel文件"""
        try:
            from datetime import datetime
            import os
            # 获取当前时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            predict_type = self.ai_predict_type_var.get()
            ai_source = self.ai_source_var.get()  # 获取AI来源
            model_name = self.ai_model_var.get()
            safe_model_name = re.sub(r'[\\/:*?"<>|]', '-', model_name)
            # 保存HTML文件
            html_filename = f"AI预测_{predict_type}_{ai_source}_{safe_model_name}_{timestamp}.html"
            html_file_path = os.path.join(os.getcwd(), html_filename)
            # 转换为HTML格式并保存
            html_content = self.markdown_to_html(markdown_content)
            with open(html_file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            self._update_ai_result(f"预测结果已自动导出到: {html_file_path}\n", 'success')
            # 新增: 导出Excel文件
            try:
                from openpyxl import Workbook
                # 创建Excel文件
                excel_filename = f"推荐结果_{predict_type}_{ai_source}_{safe_model_name}_{timestamp}.xlsx"
                excel_file_path = os.path.join(os.getcwd(), excel_filename)
                # 创建工作簿和工作表
                wb = Workbook()
                ws = wb.active
                ws.title = "AI预测推荐结果"
                # 添加表头 (与比赛选择列表保持一致，并新增"预测结论与投注推荐"列)
                headers = ["序号", "联赛", "对阵", "开赛时间", "比赛状态", "比分", "让球盘口", "大小盘口", "预测结论与投注推荐"]
                ws.append(headers)
                # 从已预测的比赛中提取数据
                predictions = self._extract_predictions_from_results(markdown_content)
                # 填充数据
                for idx, match_data in enumerate(predictions, 1):
                    row = [
                        idx,
                        match_data.get("league", ""),
                        match_data.get("teams", ""),
                        match_data.get("time", ""),
                        match_data.get("status", ""),
                        match_data.get("score", ""),
                        match_data.get("handicap", ""),
                        match_data.get("ou_handicap", ""),
                        match_data.get("prediction", "")
                    ]
                    ws.append(row)
                # 调整列宽
                for column in ws.columns:
                    max_length = 0
                    column = list(column)
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 80)  # 最大宽度限制为80个字符
                    ws.column_dimensions[column[0].column_letter].width = adjusted_width
                # 保存Excel文件
                wb.save(excel_file_path)
                self._update_ai_result(f"预测推荐结果已导出到Excel: {excel_file_path}\n", 'success')
            except ImportError:
                self._update_ai_result("缺少openpyxl库，无法导出Excel文件。请安装：pip install openpyxl\n", 'error')
            except Exception as e:
                self._update_ai_result(f"导出Excel文件失败: {str(e)}\n", 'error')
        except Exception as e:
            self._update_ai_result(f"自动导出结果失败: {str(e)}\n", 'error')
    
    def _update_ai_result(self, text, tag='info'):
        """更新AI结果文本（线程安全）"""
        self.root.after(0, lambda t=text, tag=tag: self.ai_result_text.insert(tk.END, t, tag))
        self.root.after(0, lambda: self.ai_result_text.see(tk.END))
    
    def _prepare_ai_data_source(self):
        """准备AI分析需要的数据源"""
        data_source = {}
        
        # 检查是否有一键抓取的数据
        if hasattr(self, 'all_odds_data') and any(self.all_odds_data.values()):
            data_source = {
                '大小盘': self.all_odds_data.get('大小盘', []),
                '亚盘': self.all_odds_data.get('亚盘', []),
                '欧盘': self.all_odds_data.get('欧盘', [])
            }
        # 如果没有，使用明细数据
        elif hasattr(self, 'detail_data') and self.detail_data:
            data_source = {
                '大小盘': [d for d in self.detail_data if d.get('odds_type') == '大小盘'],
                '亚盘': [d for d in self.detail_data if d.get('odds_type') == '亚盘'],
                '欧盘': [d for d in self.detail_data if d.get('odds_type') == '欧盘']
            }
        
        return data_source
    
    def _collect_match_data_for_ai(self, match_id, data_source):
        """收集特定比赛的数据"""
        match_data = {
            'match_id': match_id,
            '大小盘': [],
            '亚盘': [],
            '欧盘': []
        }
        
        # 收集各类盘口数据
        for odds_type, data_list in data_source.items():
            for data in data_list:
                if str(data.get('matchId', '')) == str(match_id):
                    # 记录比赛队伍信息
                    if 'home_team' in data and 'away_team' in data:
                        match_data['home_team'] = data['home_team']
                        match_data['away_team'] = data['away_team']
                    
                    # 记录盘口数据
                    match_data[odds_type].append(data)
        
        # 按时间排序各类数据
        for odds_type in ['大小盘', '亚盘', '欧盘']:
            match_data[odds_type].sort(key=lambda x: int(x.get('createTime', 0)))
        
        # 验证是否有足够的数据
        if not match_data.get('home_team') or not any(match_data[k] for k in ['大小盘', '亚盘', '欧盘']):
            return None
            
        return match_data
    
    def _build_ai_prompt(self, match_id, match_name, match_data, predict_type):
        """构建AI提示词"""
        home_team = match_data.get('home_team', '主队')
        away_team = match_data.get('away_team', '客队')
        
        prompt = f"请作为专业足球分析师分析以下比赛: {home_team} VS {away_team}，提供详细的{predict_type}预测分析。\n\n"
        prompt += "以下是该比赛的赔率和盘口数据:\n\n"
        
        # 添加大小盘数据
        if match_data['大小盘']:
            prompt += "==== 大小盘数据 ====\n"
            
            # 对于篇幅考虑，只取最早和最新的几条记录
            ou_data = match_data['大小盘']
            if len(ou_data) > 10:
                ou_data = ou_data[:3] + ou_data[-7:]
                prompt += f"(共{len(match_data['大小盘'])}条数据，仅显示最早3条和最新7条)\n"
                
            for idx, data in enumerate(ou_data):
                data_type = data.get('type', '')
                
                # 转换数据类型为中文
                if data_type == 'roll':
                    data_type = '走地盘'
                elif data_type == 'index':
                    data_type = '即时盘'
                elif data_type == 'breakfast':
                    data_type = '早盘'
                
                line = data.get('d', '')  # 盘口
                over_odds = data.get('c', '')  # 大球赔率
                under_odds = data.get('e', '')  # 小球赔率
                time_str = data.get('changeTimeStr', '')  # 时间
                
                prompt += f"{idx+1}. {data_type} | 盘口: {line} | 大球: {over_odds} | 小球: {under_odds} | 时间: {time_str}\n"
            
            prompt += "\n"
        
        # 添加亚盘数据
        if match_data['亚盘']:
            prompt += "==== 亚盘数据 ====\n"
            
            # 对于篇幅考虑，只取最早和最新的几条记录
            asian_data = match_data['亚盘']
            if len(asian_data) > 10:
                asian_data = asian_data[:3] + asian_data[-7:]
                prompt += f"(共{len(match_data['亚盘'])}条数据，仅显示最早3条和最新7条)\n"
                
            for idx, data in enumerate(asian_data):
                data_type = data.get('type', '')
                
                # 转换数据类型为中文
                if data_type == 'roll':
                    data_type = '走地盘'
                elif data_type == 'index':
                    data_type = '即时盘'
                elif data_type == 'breakfast':
                    data_type = '早盘'
                
                line = data.get('d', '')  # 盘口
                home_odds = data.get('c', '')  # 主队赔率
                away_odds = data.get('e', '')  # 客队赔率
                time_str = data.get('changeTimeStr', '')  # 时间
                
                prompt += f"{idx+1}. {data_type} | 盘口: {line} | 主队: {home_odds} | 客队: {away_odds} | 时间: {time_str}\n"
            
            prompt += "\n"
        
        # 添加欧盘数据
        if match_data['欧盘']:
            prompt += "==== 欧盘数据 ====\n"
            
            # 对于篇幅考虑，只取最早和最新的几条记录
            euro_data = match_data['欧盘']
            if len(euro_data) > 10:
                euro_data = euro_data[:3] + euro_data[-7:]
                prompt += f"(共{len(match_data['欧盘'])}条数据，仅显示最早3条和最新7条)\n"
                
            for idx, data in enumerate(euro_data):
                data_type = data.get('type', '')
                
                # 转换数据类型为中文
                if data_type == 'roll':
                    data_type = '走地盘'
                elif data_type == 'index':
                    data_type = '即时盘'
                elif data_type == 'breakfast':
                    data_type = '早盘'
                
                home_win = data.get('c1', '')  # 主胜赔率
                draw = data.get('c2', '')  # 平局赔率
                away_win = data.get('c3', '')  # 主负赔率
                time_str = data.get('changeTimeStr', '')  # 时间
                
                prompt += f"{idx+1}. {data_type} | 主胜: {home_win} | 平局: {draw} | 客胜: {away_win} | 时间: {time_str}\n"
            
            prompt += "\n"
        
        # 添加具体的分析请求
        prompt += f"请根据上述数据分析这场比赛的{predict_type}，给出具体的预测结果。"
        
        # 不同预测类型的具体要求
        if predict_type == "胜平负":
            prompt += "请详细分析主队获胜、平局或客队获胜的概率，并给出最终推荐。"
        elif predict_type == "大小球":
            prompt += "请详细分析比赛的进球数是否会超过盘口，并给出大球或小球的最终推荐。"
        elif predict_type == "让球":
            prompt += "请详细分析考虑让球后的比赛结果，并给出让球盘的投注推荐。"
        elif predict_type == "综合分析":
            prompt += "请全面分析比赛可能的结果，包括胜平负、大小球和让球等多个维度，并给出最有价值的投注推荐。"
        
        prompt += "\n\n你的分析应该包含：\n1. 数据趋势分析\n2. 赔率变化意义解读\n3. 各种盘口相互印证\n4. 明确的预测结论\n5. 可信度评估"
        
        return prompt
    
    def _call_ai_model(self, ai_source, model_name, api_url, api_key, system_prompt, prompt, temperature):
        """调用AI模型获取预测结果"""
        try:
            import requests
            import json
            
            self._update_ai_result("正在请求AI模型...\n", 'info')
            
            # 根据不同的AI来源使用不同的API调用方式
            if ai_source == "Ollama":
                # Ollama API请求
                headers = {"Content-Type": "application/json"}
                
                data = {
                    "model": model_name,
                    "prompt": prompt,
                    "system": system_prompt,
                    "temperature": temperature,
                    "stream": False
                }
                
                response = requests.post(api_url, headers=headers, data=json.dumps(data))
                
                if response.status_code == 200:
                    result = response.json().get('response', '')
                    return result
                else:
                    self._update_ai_result(f"Ollama API请求失败: HTTP {response.status_code}\n", 'error')
                    return None
                    
            elif ai_source == "OpenAI":
                # OpenAI API请求
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {api_key}"
                }
                
                data = {
                    "model": model_name,
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": temperature
                }
                
                response = requests.post(api_url, headers=headers, data=json.dumps(data))
                
                if response.status_code == 200:
                    result = response.json()['choices'][0]['message']['content']
                    return result
                else:
                    error_msg = f"OpenAI API请求失败: HTTP {response.status_code}"
                    try:
                        error_msg += f"\n{response.json().get('error', {}).get('message', '')}"
                    except:
                        pass
                    self._update_ai_result(error_msg + "\n", 'error')
                    return None
            
            elif ai_source == "DeepSeek":
                # DeepSeek API请求
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {api_key}"
                }
                
                data = {
                    "model": model_name,
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": temperature
                }
                
                response = requests.post(api_url, headers=headers, data=json.dumps(data))
                
                if response.status_code == 200:
                    result = response.json()['choices'][0]['message']['content']
                    return result
                else:
                    error_msg = f"DeepSeek API请求失败: HTTP {response.status_code}"
                    try:
                        error_msg += f"\n{response.json().get('error', {}).get('message', '')}"
                    except:
                        pass
                    self._update_ai_result(error_msg + "\n", 'error')
                    return None
                    
            elif ai_source == "KIMI":
                # KIMI API请求
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {api_key}"
                }
                
                data = {
                    "model": model_name,
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": temperature
                }
                
                response = requests.post(api_url, headers=headers, data=json.dumps(data))
                
                if response.status_code == 200:
                    result = response.json()['choices'][0]['message']['content']
                    return result
                else:
                    error_msg = f"KIMI API请求失败: HTTP {response.status_code}"
                    try:
                        error_msg += f"\n{response.json().get('error', {}).get('message', '')}"
                    except:
                        pass
                    self._update_ai_result(error_msg + "\n", 'error')
                    return None
                    
            else:  # 其他API
                # 自定义API请求
                headers = {
                    "Content-Type": "application/json"
                }
                
                if api_key:
                    headers["Authorization"] = f"Bearer {api_key}"
                
                data = {
                    "model": model_name,
                    "messages": [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": prompt}
                    ],
                    "temperature": temperature
                }
                
                response = requests.post(api_url, headers=headers, data=json.dumps(data))
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        # 尝试不同可能的响应格式
                        if 'choices' in result and len(result['choices']) > 0:
                            if 'message' in result['choices'][0]:
                                return result['choices'][0]['message']['content']
                            elif 'text' in result['choices'][0]:
                                return result['choices'][0]['text']
                        elif 'response' in result:
                            return result['response']
                        elif 'content' in result:
                            return result['content']
                        else:
                            # 如果找不到常见格式，返回完整JSON
                            return str(result)
                    except:
                        self._update_ai_result("API响应格式解析失败\n", 'error')
                        return str(response.text)
                else:
                    self._update_ai_result(f"API请求失败: HTTP {response.status_code}\n", 'error')
                    return None
                    
        except Exception as e:
            self._update_ai_result(f"调用AI模型失败: {str(e)}\n", 'error')
            import traceback
            self._update_ai_result(f"错误详情: {traceback.format_exc()}\n", 'error')
            return None

    # 添加AI预测计时器相关函数
    def start_ai_predict_timer(self):
        """启动AI预测计时器"""
        self.ai_predict_start_time = time.time()
        self.ai_predict_timer_running = True
        self.update_ai_predict_duration()
        
    def stop_ai_predict_timer(self):
        """停止AI预测计时器"""
        self.ai_predict_timer_running = False
        if self.ai_predict_timer_id:
            self.root.after_cancel(self.ai_predict_timer_id)
            self.ai_predict_timer_id = None
        
    def update_ai_predict_duration(self):
        """更新AI预测持续时间显示"""
        if not self.ai_predict_timer_running:
            return
            
        if self.ai_predict_start_time:
            elapsed_seconds = int(time.time() - self.ai_predict_start_time)
            hours, remainder = divmod(elapsed_seconds, 3600)
            minutes, seconds = divmod(remainder, 60)
            duration_str = f"AI预测已用时: {hours:02d}:{minutes:02d}:{seconds:02d}"
            self.ai_predict_duration_var.set(duration_str)
            
        # 每秒更新一次
        self.ai_predict_timer_id = self.root.after(1000, self.update_ai_predict_duration)
    
    def _extract_predictions_from_results(self, markdown_content):
        """从AI预测结果Markdown文本中提取结构化的预测数据，用于Excel导出
        
        返回格式：[{
            'teams': '主队 VS 客队', 
            'league': '联赛名称',
            'time': '比赛时间',
            'status': '比赛状态', 
            'score': '比分',
            'handicap': '盘口',
            'prediction': '预测结论与投注推荐'
        }, ...]
        """
        predictions = []
        
        # 从AI预测tab的Treeview中获取比赛基本信息
        selected_items = self.ai_match_tree.selection()
        match_info_map = {}
        
        for item_id in selected_items:
            match_id = self.ai_match_map.get(item_id)
            if match_id:
                item_values = self.ai_match_tree.item(item_id, 'values')
                if len(item_values) >= 8:  # 确保有足够的列数据(更新为8个字段)
                    match_info = {
                        'league': item_values[1],
                        'teams': item_values[2],
                        'time': item_values[3],
                        'status': item_values[4],
                        'score': item_values[5],
                        'handicap': item_values[6],
                        'ou_handicap': item_values[7],
                        'match_id': match_id
                    }
                    match_info_map[match_id] = match_info
        
        # 解析Markdown内容，识别每场比赛的预测结果
        import re
        
        # 找到所有比赛标题和对应的预测内容
        match_sections = re.split(r'## \【\d+/\d+\】正在分析:', markdown_content)
        if len(match_sections) <= 1:
            # 尝试其他可能的分隔模式
            match_sections = re.split(r'## \【\d+/\d+\】', markdown_content)
        
        # 第一部分通常是引言，忽略
        match_sections = match_sections[1:] if len(match_sections) > 1 else []
        
        for section in match_sections:
            # 提取比赛名称
            match_name = section.strip().split('\n')[0].strip() if section.strip() else ""
            
            # 查找匹配的比赛信息
            match_info = None
            for match_id, info in match_info_map.items():
                # 检查比赛名称是否在section中
                if info['teams'] in match_name or match_name in info['teams']:
                    match_info = info
                    break
            
            if match_info is None:
                continue
                
            # 提取预测结论，寻找"结论"、"预测"、"推荐"等关键词所在段落
            prediction = ""
            lines = section.split('\n')
            
            # 定义可能包含预测结论的标题关键词
            conclusion_headers = [
                "预测结论与投注推荐",
                "预测结论",
                "投注推荐",
                "结论",
                "推荐",
                "建议",
                "总结"
            ]
            
            # 首先尝试查找完整的"预测结论与投注推荐"部分
            # 通常这部分会以标题开始，比如"#### 4. 预测结论与投注推荐"
            section_start = -1
            section_end = -1
            
            for i, line in enumerate(lines):
                # 查找标题行
                for header in conclusion_headers:
                    if header in line:
                        section_start = i
                        break
                
                # 如果找到了起始位置，寻找下一个同级或更高级标题作为结束位置
                if section_start >= 0 and i > section_start:
                    if line.startswith('####') or line.startswith('###') or line.startswith('##'):
                        section_end = i
                        break
            
            # 如果找到了完整的部分
            if section_start >= 0:
                if section_end == -1:  # 如果没有找到结束位置，则取到末尾
                    section_end = len(lines)
                
                # 提取该部分内容
                conclusion_section = '\n'.join(lines[section_start:section_end])
                
                # 进一步查找"推荐盘口"、"预测比分"等关键信息
                extract_parts = []
                
                # 定义要提取的关键点
                key_points = [
                    r'推荐盘口[：:](.*?)(?=\n\n|\Z)',
                    r'预测比分[：:](.*?)(?=\n\n|\Z)',
                    r'依据[：:](.*?)(?=\n\n|\Z)',
                    r'建议[：:](.*?)(?=\n\n|\Z)'
                ]
                
                for point in key_points:
                    matches = re.search(point, conclusion_section, re.DOTALL)
                    if matches:
                        extract_parts.append(matches.group(0))
                
                if extract_parts:
                    prediction = '\n'.join(extract_parts)
                else:
                    # 如果没有找到具体的关键点，使用整个结论部分
                    prediction = conclusion_section
            
            # 如果没有找到专门的结论部分，尝试从整个内容中提取结论
            if not prediction:
                # 查找包含关键词的行及其后面几行
                conclusion_patterns = [
                    r'结论[:：].*', 
                    r'预测[:：].*',
                    r'推荐[:：].*',
                    r'推荐盘口[:：].*',
                    r'建议[:：].*',
                    r'总结[:：].*'
                ]
                
                for i, line in enumerate(lines):
                    for pattern in conclusion_patterns:
                        if re.search(pattern, line, re.IGNORECASE):
                            # 找到关键行，提取这一行及后续几行作为预测结论
                            start_idx = i
                            end_idx = min(i + 10, len(lines))  # 最多取10行
                            
                            # 如果遇到新的段落标题或者分隔符，提前结束
                            for j in range(start_idx + 1, end_idx):
                                if j < len(lines) and (lines[j].startswith('#') or lines[j].startswith('---')):
                                    end_idx = j
                                    break
                            
                            prediction = '\n'.join(lines[start_idx:end_idx])
                            break
                    if prediction:
                        break
            
            # 如果还是没找到，尝试提取"总结"部分
            if not prediction:
                total_text = '\n'.join(lines)
                summary_match = re.search(r'总结[：:](.*?)(?=\n\n|\Z)', total_text, re.DOTALL)
                if summary_match:
                    prediction = summary_match.group(0)
            
            # 如果依然没有找到预测结论，则提取AI预测结果中的一部分
            if not prediction and "AI预测结果" in section:
                result_part = ""
                if "AI预测结果:" in section:
                    result_part = section.split("AI预测结果:")[1].strip()
                elif "AI预测结果：" in section:
                    result_part = section.split("AI预测结果：")[1].strip()
                
                # 在结果中查找关键段落
                if result_part:
                    for pattern in [r'推荐盘口.*?(?=\n\n|\Z)', r'预测比分.*?(?=\n\n|\Z)', r'依据.*?(?=\n\n|\Z)']:
                        matches = re.search(pattern, result_part, re.DOTALL)
                        if matches:
                            if not prediction:
                                prediction = matches.group(0)
                            else:
                                prediction += '\n' + matches.group(0)
                    
                    # 如果没有找到关键段落，取前300字符
                    if not prediction:
                        prediction = result_part[:300] + ("..." if len(result_part) > 300 else "")
            
            # 如果仍然没有找到任何预测内容，使用最后的备选方案
            if not prediction:
                prediction = "未能提取到明确的预测结论"
                
            # 将提取到的预测结论添加到比赛信息中
            match_info_copy = match_info.copy()
            match_info_copy['prediction'] = prediction
            predictions.append(match_info_copy)
        
        return predictions

    def _fetch_single_match_for_detail(self, task, results_list, task_counter):
        """抓取单场比赛单一盘口数据的线程函数（直接API请求方式）"""
        # 保留接口但无实际功能
        pass

    def refresh_detail_matches(self):
        """刷新明细tab的比赛选择列表"""
        self.detail_match_tree.delete(*self.detail_match_tree.get_children())
        self.detail_match_map = {}
        match_list = []
        if hasattr(self, 'last_response_json') and self.last_response_json:
            if isinstance(self.last_response_json, dict):
                if 'data' in self.last_response_json and 'list' in self.last_response_json['data']:
                    match_list = self.last_response_json['data']['list']
                elif 'list' in self.last_response_json:
                    match_list = self.last_response_json['list']
            elif isinstance(self.last_response_json, list):
                match_list = self.last_response_json

        # 获取当前筛选设置
        filter_mode = self.match_display_filter_var.get()

        display_idx = 1  # 用于显示的序号
        for idx, match in enumerate(match_list, 1):
            match_id = match.get('ID', '')
            home_team = match.get('home', '')
            away_team = match.get('away', '')
            league = match.get('league', '')
            time_str = match.get('time', '')
            state = match.get('stateDesc', '')
            home_score = match.get('homeScore', '0')
            away_score = match.get('awayScore', '0')
            score = f"{home_score}-{away_score}"
            if match_id and home_team and away_team:
                display_text = f"{home_team} VS {away_team}"
                # 获取让球盘口信息
                handicap = match.get("JSPKDesc", "")
                # 获取大小球盘口信息
                ou_handicap = match.get("DXQDesc", "")

                # 应用与主页面相同的筛选逻辑
                should_display = True
                if filter_mode == "只显示有数据赛事":
                    # 检查让球盘口和大小球盘口是否都为空或"-"
                    if (not handicap or handicap == "-") and (not ou_handicap or ou_handicap == "-"):
                        should_display = False

                # 只有通过筛选的比赛才显示
                if should_display:
                    item_id = self.detail_match_tree.insert('', 'end', values=(display_idx, league, display_text, time_str, state, score, handicap, ou_handicap))
                    self.detail_match_map[item_id] = match_id
                    display_idx += 1

    def select_all_detail_matches(self):
        """明细tab比赛全选"""
        for item in self.detail_match_tree.get_children():
            self.detail_match_tree.selection_add(item)

    def deselect_all_detail_matches(self):
        """明细tab比赛取消选择"""
        self.detail_match_tree.selection_remove(*self.detail_match_tree.get_children())

    def on_match_type_changed(self, event=None):
        """赛事类型变更时处理"""
        match_type = self.match_type_var.get()
        # 当选择胜负彩赛事时显示期数输入框，否则隐藏
        if match_type == "胜负彩赛事":
            if hasattr(self, 'sfb_issue_label') and hasattr(self, 'sfb_issue_entry'):
                self.sfb_issue_label.pack(side=tk.LEFT, padx=(15, 2))
                self.sfb_issue_entry.pack(side=tk.LEFT, padx=2)
        else:
            if hasattr(self, 'sfb_issue_label') and hasattr(self, 'sfb_issue_entry'):
                self.sfb_issue_label.pack_forget()
                self.sfb_issue_entry.pack_forget()

        self.log_message(f"已切换赛事类型为: {match_type}", 'info')

    def on_match_filter_changed(self):
        """赛事筛选变更时处理"""
        filter_mode = self.match_display_filter_var.get()
        self.log_message(f"已切换赛事筛选为: {filter_mode}", 'info')

        # 如果已经有比赛数据，重新应用筛选
        if hasattr(self, 'last_response_json') and self.last_response_json:
            if isinstance(self.last_response_json, dict) and 'data' in self.last_response_json and 'list' in self.last_response_json['data']:
                match_list = self.last_response_json['data']['list']
                self.update_main_match_tree(match_list)
            elif isinstance(self.last_response_json, dict) and 'list' in self.last_response_json:
                match_list = self.last_response_json['list']
                self.update_main_match_tree(match_list)
            elif isinstance(self.last_response_json, list):
                match_list = self.last_response_json
                self.update_main_match_tree(match_list)

    def fetch_sfb_matches_api(self):
        """获取胜负彩赛事数据"""
        try:
            import json, hashlib, time, requests
            from datetime import datetime
            
            # 胜负彩赛事专用API URL
            api_url = "https://app.zqcf718.com/v11/game/fourteenIssueScheduleGame"
            self.log_message(f"使用胜负彩专用API", 'info')
            
            # 获取期数
            issue = self.sfb_issue_var.get()
            if not issue:
                self.log_message("胜负彩期数不能为空", 'error')
                self.update_status("请求失败")
                self.root.after(0, lambda: self.start_listen_btn.config(state=tk.NORMAL))
                self.root.after(0, lambda: self.stop_btn.config(state=tk.DISABLED))
                return
            
            # 准备请求参数 - 胜负彩专用格式
            current_time_millis = int(round(time.time() * 1000))
            params = {
                "issue": issue,
                "lotteryId": "3",
                "device_type": 1,
                "version": "6.7.0",
                "platform": "android",
                "version_code": 67,
                "sid": 8001,
                "v": 1,
                "device_id": "5ffafcc359def39c",
                "deviceId": "5ffafcc359def39c",
                "pushRegisterId": "1104a897939cc9db155",
                "systemVersion": "12",
                "deviceModel": "PGEM10",
                "deviceBrand": "OPPO",
                "timeInMillis": current_time_millis,
                "timeZone": "GMT+08:00"
            }
            
            # 构建请求体
            request_body = {"params": params}
            
            # 设置请求头 - 胜负彩专用
            headers = {
                'Host': 'app.zqcf718.com',
                'Content-Type': 'application/json;charset=utf-8',
                'Accept-Encoding': 'gzip, deflate, br',
                'User-Agent': 'okhttp/4.9.3',
                'Connection': 'close'
            }
            
            # 发送请求
            self.log_message("正在发送胜负彩赛事数据请求...", 'info')
            response = requests.post(api_url, json=request_body, headers=headers)
            
            # 处理响应
            if response.status_code == 200:
                self.log_message("请求成功", 'success')
                response_json = response.json()
                self.last_response_json = response_json
                
                # 添加诊断日志，打印响应结构
                try:
                    # structure_info = f"响应结构: data类型={type(response_json['data'])}"
                    # if 'data' in response_json:
                    #     if isinstance(response_json['data'], dict):
                    #         structure_info += f", 字段={list(response_json['data'].keys())}"
                    #     elif isinstance(response_json['data'], list) and response_json['data']:
                    #         structure_info += f", 列表长度={len(response_json['data'])}"
                    #         if response_json['data'] and isinstance(response_json['data'][0], dict):
                    #             structure_info += f", 第一项字段={list(response_json['data'][0].keys())}"
                    self.log_message('胜负彩专用API请求成功','info')
                except Exception as e:
                    self.log_message(f"诊断信息生成失败: {str(e)}", 'warning')
                
                # 处理胜负彩专用响应格式，提取出与常规格式相同的字段
                if response_json and 'data' in response_json:
                    try:
                        match_list = []
                        
                        # 确定正确的数据源 - 检查data是字典还是列表
                        if isinstance(response_json['data'], dict) and 'match_list' in response_json['data']:
                            source_list = response_json['data']['match_list']
                        elif isinstance(response_json['data'], list):
                            source_list = response_json['data']  # 如果data本身就是列表
                        else:
                            source_list = []
                            
                        self.log_message(f"获取到 {len(source_list)} 场胜负彩赛事", 'success')
                        
                        # 如果没有比赛数据，给出明确提示
                        if not source_list:
                            self.log_message("未找到任何胜负彩赛事数据，请检查期数是否正确", 'warning')
                            self.update_status("未找到赛事数据")
                            self.root.after(0, lambda: self.start_listen_btn.config(state=tk.NORMAL))
                            self.root.after(0, lambda: self.stop_btn.config(state=tk.DISABLED))
                            return
                        
                        # 将胜负彩格式转换为通用格式
                        for idx, match in enumerate(source_list):
                            # 辅助函数：尝试多个可能的字段名获取值
                            def try_get(obj, keys, default=''):
                                for key in keys:
                                    if key in obj:
                                        return obj[key]
                                return default
                            
                            # 字段映射转换 - 考虑多种可能的字段名
                            match_converted = {
                                "ID": str(try_get(match, ['match_id', 'id', 'ID'])),
                                "league": try_get(match, ['league_name', 'league', 'leagueName']),
                                "home": try_get(match, ['home_name', 'home', 'homeName', 'homeTeam']),
                                "away": try_get(match, ['guest_name', 'away', 'awayName', 'guestName', 'awayTeam']),
                                "homeScore": try_get(match, ['home_score', 'homeScore'], '0'),
                                "awayScore": try_get(match, ['guest_score', 'awayScore', 'guestScore'], '0'),
                                "time": try_get(match, ['match_time', 'time', 'matchTime']),
                                "stateDesc": try_get(match, ['status_desc', 'statusDesc', 'status', 'stateDesc']),
                                "JSPKDesc": try_get(match, ['rq_desc', 'rqDesc', 'JSPKDesc']),  # 让球盘口
                                "DXQDesc": try_get(match, ['dx_desc', 'dxDesc', 'DXQDesc']),    # 大小球盘口
                                "HJSPL": try_get(match, ['letball_home', 'HJSPL']),             # 主队赔率
                                "WJSPL": try_get(match, ['letball_guest', 'WJSPL']),            # 客队赔率
                                "DXQ_HJSPL": try_get(match, ['size_big', 'DXQ_HJSPL']),         # 大球赔率
                                "DXQ_WJSPL": try_get(match, ['size_small', 'DXQ_WJSPL']),       # 小球赔率
                                # 额外添加胜负彩特有字段
                                "issue": issue,
                                "match_no": try_get(match, ['match_no', 'matchNo'])
                            }
                            match_list.append(match_converted)
                        
                        # 使用转换后的通用格式数据，与普通请求输出保持一致
                        self.last_response_json = {'data': {'list': match_list}}
                        self.update_main_match_tree(match_list)
                    except Exception as e:
                        self.log_message(f"处理胜负彩数据时发生错误: {str(e)}", 'error')
                        import traceback
                        self.log_message(f"详细错误: {traceback.format_exc()}", 'error')
                else:
                    self.log_message("响应数据格式异常，请检查期数是否正确", 'warning')
            else:
                self.log_message(f"请求失败，状态码: {response.status_code}", 'error')
                self.log_message(f"响应内容: {response.text}", 'error')
            
            self.update_status("数据获取完成")
        except Exception as e:
            self.log_message(f"获取胜负彩赛事数据时发生异常: {str(e)}", 'error')
            self.update_status("数据获取异常")
        finally:
            self.root.after(0, lambda: self.start_listen_btn.config(state=tk.NORMAL))
            self.root.after(0, lambda: self.stop_btn.config(state=tk.DISABLED))

    def setup_index_reference_ui(self, parent_frame):
        """设置指数参考tab的UI"""
        # 配置区
        config_frame = ttk.LabelFrame(parent_frame, text="配置区")
        config_frame.pack(fill=tk.X, padx=5, pady=5)

        # 日期选择
        date_row = ttk.Frame(config_frame)
        date_row.pack(fill=tk.X, pady=2)

        ttk.Label(date_row, text="查询日期:").pack(side=tk.LEFT, padx=5)

        # 创建日期变量，默认为当天
        from datetime import datetime
        today = datetime.now().strftime('%Y-%m-%d')
        self.index_date_var = tk.StringVar(value=today)

        # 日期输入框
        self.index_date_entry = ttk.Entry(date_row, textvariable=self.index_date_var, width=12)
        self.index_date_entry.pack(side=tk.LEFT, padx=5)

        # 操作区
        operation_frame = ttk.LabelFrame(parent_frame, text="操作区")
        operation_frame.pack(fill=tk.X, padx=5, pady=5)

        # 获取指数按钮
        self.fetch_index_btn = ttk.Button(operation_frame, text="获取指数", command=self.fetch_index_data)
        self.fetch_index_btn.pack(side=tk.LEFT, padx=5, pady=5)

        # 导出Excel按钮
        self.export_index_btn = ttk.Button(operation_frame, text="导出Excel", command=self.export_index_to_excel)
        self.export_index_btn.pack(side=tk.RIGHT, padx=5, pady=5)

        # 指数列表显示区
        list_frame = ttk.LabelFrame(parent_frame, text="指数列表")
        list_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建Treeview组件显示指数数据 - 优化后的格式
        columns = ("序号", "赛事类型", "指数阶段", "主队", "客队", "赛事状态", "开赛时间", "当前比分",
                  "主队指数", "亚指盘口", "客队指数", "胜指数", "平指数", "负指数",
                  "大球指数", "大小盘口", "小球指数")

        self.index_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)

        # 设置列宽和表头 - 根据新格式调整
        column_widths = [40, 80, 60, 100, 100, 60, 120, 60, 60, 60, 60, 60, 60, 60, 60, 60, 60]
        for i, (col, width) in enumerate(zip(columns, column_widths)):
            self.index_tree.column(col, width=width, anchor="center")
            self.index_tree.heading(col, text=col)

        # 添加滚动条
        index_scrollbar_v = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.index_tree.yview)
        index_scrollbar_h = ttk.Scrollbar(list_frame, orient=tk.HORIZONTAL, command=self.index_tree.xview)
        self.index_tree.configure(yscrollcommand=index_scrollbar_v.set, xscrollcommand=index_scrollbar_h.set)

        # 布局
        self.index_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        index_scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        index_scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)

        # 状态显示区
        status_frame = ttk.LabelFrame(parent_frame, text="状态信息")
        status_frame.pack(fill=tk.X, padx=5, pady=5)

        self.index_status_var = tk.StringVar(value="就绪")
        self.index_status_label = ttk.Label(status_frame, textvariable=self.index_status_var)
        self.index_status_label.pack(pady=5)

    def fetch_index_data(self):
        """获取指数数据"""
        if self.index_fetching:
            return

        # 验证日期格式
        date_str = self.index_date_var.get()
        try:
            from datetime import datetime
            datetime.strptime(date_str, '%Y-%m-%d')
        except ValueError:
            self.index_status_var.set("日期格式错误，请使用 YYYY-MM-DD 格式")
            return

        self.index_fetching = True
        self.fetch_index_btn.config(state=tk.DISABLED)
        self.index_status_var.set("正在获取指数数据...")
        self.index_stop_event.clear()

        # 启动获取线程
        thread = threading.Thread(target=self._fetch_index_thread, daemon=True)
        thread.start()

    def _fetch_index_thread(self):
        """指数获取线程"""
        try:
            # 清空现有数据
            self.index_data = []

            # 依次获取三种指数类型
            index_types = [
                ("0", "亚指"),  # indexType: 0 = 亚指
                ("1", "欧指"),  # indexType: 1 = 欧指
                ("2", "大小")   # indexType: 2 = 大小指数
            ]

            # 存储所有比赛的指数数据，按比赛分组
            match_index_data = {}

            for index_type_id, index_type_name in index_types:
                if self.index_stop_event.is_set():
                    break

                self.root.after(0, lambda name=index_type_name: self.index_status_var.set(f"正在获取{name}数据..."))

                # 调用API获取指数数据
                response_data = self._call_index_api(index_type_id)
                if response_data:
                    # 处理响应数据，按比赛分组
                    self._process_index_response_grouped(response_data, index_type_name, match_index_data)

            # 按比赛排序并生成最终数据
            self._generate_sorted_index_data(match_index_data)

            # 更新UI显示
            self.root.after(0, self._update_index_display)

            # 自动导出Excel文件
            self.root.after(0, self._auto_export_index_data)

            self.root.after(0, lambda: self.index_status_var.set(f"获取完成，共 {len(self.index_data)} 条指数数据，已自动导出Excel"))

        except Exception as e:
            self.root.after(0, lambda: self.index_status_var.set(f"获取指数数据时发生错误: {str(e)}"))
        finally:
            self.index_fetching = False
            self.root.after(0, lambda: self.fetch_index_btn.config(state=tk.NORMAL))

    def _call_index_api(self, index_type):
        """调用指数API"""
        try:
            import time

            # API地址
            api_url = "https://app1011.zqcf618.com/match/v11/indexNumber/list"

            # 生成时间戳
            current_time_millis = int(round(time.time() * 1000))

            # 获取用户选择的日期
            query_date = self.index_date_var.get()

            # 构建请求参数
            params = {
                "indexType": index_type,
                "type": "0",
                "leagues": [],
                "time": query_date,  # 使用用户选择的日期
                "lotteryIdList": ["3"],
                "device_type": 1,
                "version": "6.7.0",
                "platform": "android",
                "version_code": 67,
                "sid": 8001,
                "v": 1,
                "device_id": "5ffafcc359def39c",
                "deviceId": "5ffafcc359def39c",
                "pushRegisterId": "1104a897939cc9db155",
                "systemVersion": "12",
                "deviceModel": "PGEM10",
                "deviceBrand": "OPPO",
                "timeInMillis": current_time_millis,
                "timeZone": "GMT+08:00"
            }

            # 构建请求体
            request_body = {"params": params}

            # 设置请求头
            headers = {
                'Host': 'app1011.zqcf618.com',
                'Content-Type': 'application/json;charset=utf-8',
                'Accept-Encoding': 'gzip, deflate, br',
                'User-Agent': 'okhttp/4.9.3',
                'Connection': 'close'
            }

            # 发送请求
            response = requests.post(api_url, json=request_body, headers=headers, timeout=30)

            if response.status_code == 200:
                return response.json()
            else:
                print(f"API请求失败: HTTP {response.status_code}")
                return None

        except Exception as e:
            print(f"调用指数API时发生错误: {str(e)}")
            return None

    def _process_index_response_grouped(self, response_data, index_type_name, match_index_data):
        """处理指数响应数据，按比赛分组"""
        try:
            if not response_data or 'data' not in response_data:
                return

            content_list = response_data['data'].get('contentList', [])
            if not content_list:
                return

            # 为每场比赛生成指数数据
            for match in content_list:
                # 获取基本比赛信息
                match_id = match.get('g', '')
                league = match.get('a', '')  # 赛事类型
                home_team = match.get('e', '')  # 主队
                away_team = match.get('f', '')  # 客队
                match_state = match.get('d', '')  # 赛事状态
                if not match_state:
                    match_state = "未"
                match_time = match.get('b', '')  # 开赛时间
                home_score = match.get('h', 0)  # 主队比分
                away_score = match.get('i', 0)  # 客队比分
                current_score = f"{home_score}-{away_score}"

                # 创建比赛唯一标识
                match_key = f"{home_team}_vs_{away_team}_{match_time}"

                # 如果这个比赛还没有记录，初始化
                if match_key not in match_index_data:
                    match_index_data[match_key] = {
                        'basic_info': {
                            'league': league,
                            'home_team': home_team,
                            'away_team': away_team,
                            'match_state': match_state,
                            'match_time': match_time,
                            'current_score': current_score
                        },
                        'index_data': {}
                    }

                # 获取指数数据
                index_list = match.get('indexLotteryIndexList', [])
                if index_list:
                    # 取第一个指数公司的数据（通常是皇冠）
                    index_data = index_list[0]
                    match_index_data[match_key]['index_data'][index_type_name] = index_data

        except Exception as e:
            print(f"处理指数响应数据时发生错误: {str(e)}")

    def _generate_sorted_index_data(self, match_index_data):
        """生成按比赛排序的指数数据 - 优化格式：每场比赛两行（初始/即时）"""
        self.index_data = []

        # 按比赛时间排序
        sorted_matches = sorted(match_index_data.items(),
                               key=lambda x: x[1]['basic_info']['match_time'])

        for match_key, match_info in sorted_matches:
            basic_info = match_info['basic_info']
            index_data = match_info['index_data']

            # 为每场比赛生成2行数据：初始和即时，整合所有指数类型
            # 初始化数据容器
            initial_data = {
                'asian_home': '', 'asian_handicap': '', 'asian_away': '',
                'euro_win': '', 'euro_draw': '', 'euro_lose': '',
                'ou_over': '', 'ou_handicap': '', 'ou_under': ''
            }
            current_data = {
                'asian_home': '', 'asian_handicap': '', 'asian_away': '',
                'euro_win': '', 'euro_draw': '', 'euro_lose': '',
                'ou_over': '', 'ou_handicap': '', 'ou_under': ''
            }

            # 提取亚指数据
            if '亚指' in index_data:
                asian_data = index_data['亚指']
                initial_data['asian_home'] = asian_data.get('d1', '')
                initial_data['asian_handicap'] = asian_data.get('e1', '')
                initial_data['asian_away'] = asian_data.get('f1', '')
                current_data['asian_home'] = asian_data.get('g1', '')
                current_data['asian_handicap'] = asian_data.get('h1', '')
                current_data['asian_away'] = asian_data.get('i1', '')

            # 提取欧指数据
            if '欧指' in index_data:
                euro_data = index_data['欧指']
                initial_data['euro_win'] = euro_data.get('d1', '')
                initial_data['euro_draw'] = euro_data.get('e1', '')
                initial_data['euro_lose'] = euro_data.get('f1', '')
                current_data['euro_win'] = euro_data.get('g1', '')
                current_data['euro_draw'] = euro_data.get('h1', '')
                current_data['euro_lose'] = euro_data.get('i1', '')

            # 提取大小指数数据
            if '大小' in index_data:
                ou_data = index_data['大小']
                initial_data['ou_over'] = ou_data.get('d1', '')
                initial_data['ou_handicap'] = ou_data.get('e1', '')
                initial_data['ou_under'] = ou_data.get('f1', '')
                current_data['ou_over'] = ou_data.get('g1', '')
                current_data['ou_handicap'] = ou_data.get('h1', '')
                current_data['ou_under'] = ou_data.get('i1', '')

            # 生成初始数据行
            initial_row = self._create_optimized_index_row(
                basic_info['league'], "初始",
                basic_info['home_team'], basic_info['away_team'],
                basic_info['match_state'], basic_info['match_time'], basic_info['current_score'],
                initial_data['asian_home'], initial_data['asian_handicap'], initial_data['asian_away'],
                initial_data['euro_win'], initial_data['euro_draw'], initial_data['euro_lose'],
                initial_data['ou_over'], initial_data['ou_handicap'], initial_data['ou_under']
            )
            self.index_data.append(initial_row)

            # 生成即时数据行
            current_row = self._create_optimized_index_row(
                basic_info['league'], "即时",
                basic_info['home_team'], basic_info['away_team'],
                basic_info['match_state'], basic_info['match_time'], basic_info['current_score'],
                current_data['asian_home'], current_data['asian_handicap'], current_data['asian_away'],
                current_data['euro_win'], current_data['euro_draw'], current_data['euro_lose'],
                current_data['ou_over'], current_data['ou_handicap'], current_data['ou_under']
            )
            self.index_data.append(current_row)

    def _create_optimized_index_row(self, league, index_stage, home_team, away_team,
                                   match_state, match_time, current_score,
                                   asian_home, asian_handicap, asian_away,
                                   euro_win, euro_draw, euro_lose,
                                   ou_over, ou_handicap, ou_under):
        """创建优化格式的指数行数据"""

        def convert_to_number(value):
            """将字符串转换为数字，如果转换失败则返回原值"""
            if not value or value == '':
                return ''
            try:
                # 尝试转换为浮点数
                return float(value)
            except (ValueError, TypeError):
                return value

        # 序号会在显示时动态生成
        return {
            '赛事类型': league,
            '指数阶段': index_stage,
            '主队': home_team,
            '客队': away_team,
            '赛事状态': match_state,
            '开赛时间': match_time,
            '当前比分': current_score,
            '主队指数': convert_to_number(asian_home),
            '亚指盘口': convert_to_number(asian_handicap),
            '客队指数': convert_to_number(asian_away),
            '胜指数': convert_to_number(euro_win),
            '平指数': convert_to_number(euro_draw),
            '负指数': convert_to_number(euro_lose),
            '大球指数': convert_to_number(ou_over),
            '大小盘口': convert_to_number(ou_handicap),
            '小球指数': convert_to_number(ou_under)
        }

    def _create_index_row(self, league, index_type, index_stage, home_team, away_team,
                         match_state, match_time, current_score,
                         home_index, asian_handicap, away_index,
                         win_index, draw_index, lose_index,
                         over_index, ou_handicap, under_index):
        """创建指数行数据"""

        def convert_to_number(value):
            """将字符串转换为数字，如果转换失败则返回原值"""
            if not value or value == '':
                return ''
            try:
                # 尝试转换为浮点数
                return float(value)
            except (ValueError, TypeError):
                return value

        # 序号会在显示时动态生成
        return {
            '赛事类型': league,
            '指数类型': index_type,
            '指数阶段': index_stage,
            '主队': home_team,
            '客队': away_team,
            '赛事状态': match_state,
            '开赛时间': match_time,
            '当前比分': current_score,
            '主队指数': convert_to_number(home_index),
            '亚指盘口': convert_to_number(asian_handicap),
            '客队指数': convert_to_number(away_index),
            '胜指数': convert_to_number(win_index),
            '平指数': convert_to_number(draw_index),
            '负指数': convert_to_number(lose_index),
            '大球指数': convert_to_number(over_index),
            '大小盘口': convert_to_number(ou_handicap),
            '小球指数': convert_to_number(under_index)
        }

    def _update_index_display(self):
        """更新指数显示"""
        # 清空现有显示
        self.index_tree.delete(*self.index_tree.get_children())

        # 添加数据到树形控件 - 优化格式
        for idx, row_data in enumerate(self.index_data, 1):
            values = (
                idx,  # 序号
                row_data['赛事类型'],
                row_data['指数阶段'],
                row_data['主队'],
                row_data['客队'],
                row_data['赛事状态'],
                row_data['开赛时间'],
                row_data['当前比分'],
                row_data['主队指数'],
                row_data['亚指盘口'],
                row_data['客队指数'],
                row_data['胜指数'],
                row_data['平指数'],
                row_data['负指数'],
                row_data['大球指数'],
                row_data['大小盘口'],
                row_data['小球指数']
            )
            self.index_tree.insert('', 'end', values=values)

    def _auto_export_index_data(self):
        """自动导出指数数据到当前目录"""
        if not self.index_data:
            return

        try:
            import openpyxl
            import os
            from datetime import datetime

            # 生成文件名
            current_date = self.index_date_var.get().replace('-', '')
            timestamp = datetime.now().strftime('%H%M%S')
            filename = f"指数数据_{current_date}_{timestamp}.xlsx"

            # 获取当前目录路径
            current_dir = os.getcwd()
            filepath = os.path.join(current_dir, filename)

            # 创建工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "指数数据"

            # 写入表头 - 优化格式
            headers = ["序号", "赛事类型", "指数阶段", "主队", "客队", "赛事状态", "开赛时间",
                      "当前比分", "主队指数", "亚指盘口", "客队指数", "胜指数", "平指数", "负指数",
                      "大球指数", "大小盘口", "小球指数"]

            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # 写入数据 - 优化格式
            for row_idx, row_data in enumerate(self.index_data, 2):
                ws.cell(row=row_idx, column=1, value=row_idx-1)  # 序号
                ws.cell(row=row_idx, column=2, value=row_data['赛事类型'])
                ws.cell(row=row_idx, column=3, value=row_data['指数阶段'])
                ws.cell(row=row_idx, column=4, value=row_data['主队'])
                ws.cell(row=row_idx, column=5, value=row_data['客队'])
                ws.cell(row=row_idx, column=6, value=row_data['赛事状态'])
                ws.cell(row=row_idx, column=7, value=row_data['开赛时间'])
                ws.cell(row=row_idx, column=8, value=row_data['当前比分'])
                ws.cell(row=row_idx, column=9, value=row_data['主队指数'])
                ws.cell(row=row_idx, column=10, value=row_data['亚指盘口'])
                ws.cell(row=row_idx, column=11, value=row_data['客队指数'])
                ws.cell(row=row_idx, column=12, value=row_data['胜指数'])
                ws.cell(row=row_idx, column=13, value=row_data['平指数'])
                ws.cell(row=row_idx, column=14, value=row_data['负指数'])
                ws.cell(row=row_idx, column=15, value=row_data['大球指数'])
                ws.cell(row=row_idx, column=16, value=row_data['大小盘口'])
                ws.cell(row=row_idx, column=17, value=row_data['小球指数'])

            # 设置Excel格式优化
            # 1. 冻结首行
            ws.freeze_panes = 'A2'

            # 2. 开启首行筛选功能
            ws.auto_filter.ref = f"A1:{chr(64 + len(headers))}{len(self.index_data) + 1}"

            # 3. 设置表头样式
            from openpyxl.styles import Font, PatternFill
            header_font = Font(bold=True)
            header_fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

            for col in range(1, len(headers) + 1):
                cell = ws.cell(row=1, column=col)
                cell.font = header_font
                cell.fill = header_fill

            # 保存文件
            wb.save(filepath)
            print(f"指数数据已自动导出到: {filepath}")

        except Exception as e:
            print(f"自动导出失败: {str(e)}")

    def export_index_to_excel(self):
        """导出指数数据到Excel"""
        if not self.index_data:
            self.index_status_var.set("没有数据可导出")
            return

        try:
            from tkinter import filedialog
            import openpyxl
            from datetime import datetime

            # 选择保存文件路径
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="保存指数数据",
                initialfile=f"指数数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            )

            if not filename:
                return

            # 创建工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "指数数据"

            # 写入表头 - 优化格式
            headers = ["序号", "赛事类型", "指数阶段", "主队", "客队", "赛事状态", "开赛时间",
                      "当前比分", "主队指数", "亚指盘口", "客队指数", "胜指数", "平指数", "负指数",
                      "大球指数", "大小盘口", "小球指数"]

            for col, header in enumerate(headers, 1):
                ws.cell(row=1, column=col, value=header)

            # 写入数据 - 优化格式
            for row_idx, row_data in enumerate(self.index_data, 2):
                ws.cell(row=row_idx, column=1, value=row_idx-1)  # 序号
                ws.cell(row=row_idx, column=2, value=row_data['赛事类型'])
                ws.cell(row=row_idx, column=3, value=row_data['指数阶段'])
                ws.cell(row=row_idx, column=4, value=row_data['主队'])
                ws.cell(row=row_idx, column=5, value=row_data['客队'])
                ws.cell(row=row_idx, column=6, value=row_data['赛事状态'])
                ws.cell(row=row_idx, column=7, value=row_data['开赛时间'])
                ws.cell(row=row_idx, column=8, value=row_data['当前比分'])
                ws.cell(row=row_idx, column=9, value=row_data['主队指数'])
                ws.cell(row=row_idx, column=10, value=row_data['亚指盘口'])
                ws.cell(row=row_idx, column=11, value=row_data['客队指数'])
                ws.cell(row=row_idx, column=12, value=row_data['胜指数'])
                ws.cell(row=row_idx, column=13, value=row_data['平指数'])
                ws.cell(row=row_idx, column=14, value=row_data['负指数'])
                ws.cell(row=row_idx, column=15, value=row_data['大球指数'])
                ws.cell(row=row_idx, column=16, value=row_data['大小盘口'])
                ws.cell(row=row_idx, column=17, value=row_data['小球指数'])

            # 设置Excel格式优化
            # 1. 冻结首行
            ws.freeze_panes = 'A2'

            # 2. 开启首行筛选功能
            ws.auto_filter.ref = f"A1:{chr(64 + len(headers))}{len(self.index_data) + 1}"

            # 3. 设置表头样式
            from openpyxl.styles import Font, PatternFill
            header_font = Font(bold=True)
            header_fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")

            for col in range(1, len(headers) + 1):
                cell = ws.cell(row=1, column=col)
                cell.font = header_font
                cell.fill = header_fill

            # 保存文件
            wb.save(filename)
            self.index_status_var.set(f"数据已导出到: {filename}")

        except Exception as e:
            self.index_status_var.set(f"导出失败: {str(e)}")

if __name__ == "__main__":
    root = tk.Tk()
    app = PacketListenerApp(root)
    root.mainloop()